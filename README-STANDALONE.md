# GentianAphrodite 独立应用程序

这是 GentianAphrodite 的独立版本，已完全脱离 fount 平台，可以作为独立应用程序运行。

## 系统要求

- **Deno** 1.40+ (推荐使用最新版本)
- **操作系统**: Windows, macOS, Linux
- **内存**: 至少 512MB 可用内存
- **存储**: 至少 100MB 可用空间

## 安装 Deno

### Windows
```powershell
# 使用 PowerShell
irm https://deno.land/install.ps1 | iex

# 或使用 Chocolatey
choco install deno

# 或使用 Scoop
scoop install deno
```

### macOS
```bash
# 使用 Homebrew
brew install deno

# 或使用 curl
curl -fsSL https://deno.land/install.sh | sh
```

### Linux
```bash
# 使用 curl
curl -fsSL https://deno.land/install.sh | sh

# 或使用 apt (Ubuntu/Debian)
sudo apt install deno

# 或使用 snap
sudo snap install deno
```

## 快速开始

### 1. 克隆或下载项目
```bash
git clone https://github.com/steve02081504/GentianAphrodite.git
cd GentianAphrodite
```

### 2. 创建配置文件
```bash
cp config.example.json config.json
```

编辑 `config.json` 文件，配置您的设置。

### 3. 运行应用程序

#### Shell 交互模式（默认）
```bash
deno run --allow-all app.mjs
```

#### 指定用户名和语言
```bash
deno run --allow-all app.mjs --username "Alice" --locale "en-US"
```

#### 使用自定义配置文件
```bash
deno run --allow-all app.mjs --config "./my-config.json"
```

#### 启用调试模式
```bash
deno run --allow-all app.mjs --debug
```

## 命令行选项

| 选项 | 短选项 | 描述 | 默认值 |
|------|--------|------|--------|
| `--help` | `-h` | 显示帮助信息 | - |
| `--version` | `-v` | 显示版本信息 | - |
| `--config` | `-c` | 指定配置文件路径 | - |
| `--platform` | `-p` | 指定运行平台 (discord, telegram, shell) | shell |
| `--locale` | `-l` | 指定语言 (zh-CN, en-US, ja-JP, ko-KR) | zh-CN |
| `--username` | `-u` | 指定用户名 | User |
| `--debug` | `-d` | 启用调试模式 | false |

## 配置说明

### 基础配置
- `username`: 用户名
- `platform`: 运行平台 (shell, discord, telegram)
- `locale`: 界面语言
- `debug`: 是否启用调试模式

### AI 配置
- `ai.defaultSource`: 默认 AI 源
- `ai.maxTokens`: 最大令牌数
- `ai.temperature`: 温度参数
- `ai.sources`: AI 源配置

### 内存配置
- `memory.shortTermEnabled`: 是否启用短期记忆
- `memory.longTermEnabled`: 是否启用长期记忆
- `memory.maxShortTermEntries`: 最大短期记忆条目数
- `memory.maxLongTermEntries`: 最大长期记忆条目数

### 安全配置
- `security.enableRateLimit`: 是否启用速率限制
- `security.maxRequestsPerMinute`: 每分钟最大请求数
- `security.enableContentFilter`: 是否启用内容过滤

## 平台支持

### Shell 模式
最基本的交互模式，通过命令行与 AI 对话。

```bash
deno run --allow-all app.mjs --platform shell
```

### Discord 模式（开发中）
作为 Discord 机器人运行。

```bash
deno run --allow-all app.mjs --platform discord
```

需要在配置文件中设置：
- `discord.token`: Discord 机器人令牌
- `discord.clientId`: 客户端 ID
- `discord.guildId`: 服务器 ID

### Telegram 模式 ✅
作为 Telegram 机器人运行。

```bash
deno run --allow-all app.mjs --platform telegram
```

需要在配置文件中设置：
- `telegram.token`: Telegram 机器人令牌
- `telegram.ownerUserId`: 您的 Telegram 用户 ID
- `telegram.ownerUsername`: 您的 Telegram 用户名

#### 快速设置 Telegram 机器人

1. **创建机器人**：与 [@BotFather](https://t.me/botfather) 对话，使用 `/newbot` 创建机器人
2. **获取用户 ID**：与 [@userinfobot](https://t.me/userinfobot) 对话获取您的用户 ID
3. **配置文件**：
   ```json
   {
     "platform": "telegram",
     "telegram": {
       "token": "YOUR_BOT_TOKEN",
       "ownerUserId": "YOUR_USER_ID",
       "ownerUsername": "your_username"
     }
   }
   ```
4. **启动机器人**：`deno run --allow-all app.mjs --platform telegram`

📖 详细设置指南请参考 [TELEGRAM-SETUP.md](./TELEGRAM-SETUP.md)

## 目录结构

```
GentianAphrodite/
├── app.mjs                 # 主应用程序入口
├── main.mjs               # 角色 API 实现
├── config.example.json    # 配置文件示例
├── config.json           # 用户配置文件
├── types/                # 类型定义
├── lib/                  # 核心库
├── data/                 # 数据目录
├── config/               # 配置目录
├── logs/                 # 日志目录
├── i18n/                 # 国际化文件
└── locales/              # 语言包
```

## 开发和调试

### 启用调试模式
```bash
deno run --allow-all app.mjs --debug
```

### 查看日志
日志文件保存在 `./logs/` 目录中。

### 开发模式
```bash
# 监听文件变化并自动重启
deno run --allow-all --watch app.mjs
```

## 故障排除

### 常见问题

1. **权限错误**
   - 确保使用 `--allow-all` 标志运行
   - 检查文件和目录权限

2. **配置文件错误**
   - 验证 JSON 格式是否正确
   - 检查必需的配置项是否存在

3. **内存不足**
   - 减少 `maxTokens` 设置
   - 清理内存数据

4. **网络连接问题**
   - 检查 AI 源的网络连接
   - 验证 API 密钥是否正确

### 获取帮助

如果遇到问题，请：

1. 查看日志文件
2. 启用调试模式
3. 检查配置文件
4. 提交 Issue 到 GitHub 仓库

## 许可证

本项目遵循原项目的许可证。详见 LICENSE 文件。

## 贡献

欢迎提交 Pull Request 和 Issue！

## 更新日志

### v1.0.0
- 初始独立版本发布
- 支持 Shell 交互模式
- 完整的配置系统
- 本地化支持
