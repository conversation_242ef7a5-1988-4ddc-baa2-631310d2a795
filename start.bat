@echo off
setlocal enabledelayedexpansion

REM GentianAphrodite 启动脚本
REM 用于 Windows 系统

title GentianAphrodite Launcher

REM 颜色定义（Windows 10+ 支持 ANSI 转义序列）
set "RED=[31m"
set "GREEN=[32m"
set "YELLOW=[33m"
set "BLUE=[34m"
set "NC=[0m"

REM 打印带颜色的消息
:print_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:print_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:print_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:print_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM 检查 Deno 是否安装
:check_deno
where deno >nul 2>&1
if %errorlevel% neq 0 (
    call :print_error "Deno 未安装或不在 PATH 中"
    call :print_info "请访问 https://deno.land/manual/getting_started/installation 安装 Deno"
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('deno --version ^| findstr "deno"') do set DENO_VERSION=%%i
call :print_success "检测到 Deno 版本: !DENO_VERSION!"
goto :eof

REM 检查配置文件
:check_config
if not exist "config.json" (
    if exist "config.example.json" (
        call :print_warning "未找到 config.json，正在复制示例配置文件..."
        copy "config.example.json" "config.json" >nul
        call :print_info "已创建 config.json，请根据需要修改配置"
    ) else (
        call :print_error "未找到配置文件"
        pause
        exit /b 1
    )
) else (
    call :print_success "找到配置文件: config.json"
)
goto :eof

REM 创建必要的目录
:create_directories
set DIRS=data config logs i18n locales

for %%d in (%DIRS%) do (
    if not exist "%%d" (
        mkdir "%%d"
        call :print_info "创建目录: %%d"
    )
)
goto :eof

REM 显示帮助信息
:show_help
echo GentianAphrodite 启动脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   /h, /help               显示此帮助信息
echo   /p PLATFORM             指定平台 (shell, discord, telegram)
echo   /u USERNAME             指定用户名
echo   /l LOCALE               指定语言 (zh-CN, en-US, ja-JP, ko-KR)
echo   /c CONFIG               指定配置文件路径
echo   /d                      启用调试模式
echo   /check                  仅检查环境，不启动应用
echo.
echo 示例:
echo   %~nx0                                    # 使用默认设置启动
echo   %~nx0 /p discord /u Alice               # 启动 Discord 模式
echo   %~nx0 /p telegram /l en-US /d           # 启动 Telegram 模式（英文，调试）
echo   %~nx0 /check                            # 检查环境
goto :eof

REM 解析命令行参数
:parse_args
set PLATFORM=
set USERNAME=
set LOCALE=
set CONFIG=
set DEBUG=
set CHECK_ONLY=false

:parse_loop
if "%~1"=="" goto :parse_done
if /i "%~1"=="/h" goto :show_help_and_exit
if /i "%~1"=="/help" goto :show_help_and_exit
if /i "%~1"=="/p" (
    set PLATFORM=%~2
    shift
    shift
    goto :parse_loop
)
if /i "%~1"=="/u" (
    set USERNAME=%~2
    shift
    shift
    goto :parse_loop
)
if /i "%~1"=="/l" (
    set LOCALE=%~2
    shift
    shift
    goto :parse_loop
)
if /i "%~1"=="/c" (
    set CONFIG=%~2
    shift
    shift
    goto :parse_loop
)
if /i "%~1"=="/d" (
    set DEBUG=--debug
    shift
    goto :parse_loop
)
if /i "%~1"=="/check" (
    set CHECK_ONLY=true
    shift
    goto :parse_loop
)
call :print_error "未知选项: %~1"
call :show_help
pause
exit /b 1

:show_help_and_exit
call :show_help
exit /b 0

:parse_done
goto :eof

REM 构建启动命令
:build_command
set CMD=deno run --allow-all app.mjs

if not "%PLATFORM%"=="" (
    set CMD=!CMD! --platform %PLATFORM%
)

if not "%USERNAME%"=="" (
    set CMD=!CMD! --username "%USERNAME%"
)

if not "%LOCALE%"=="" (
    set CMD=!CMD! --locale %LOCALE%
)

if not "%CONFIG%"=="" (
    set CMD=!CMD! --config "%CONFIG%"
)

if not "%DEBUG%"=="" (
    set CMD=!CMD! %DEBUG%
)

goto :eof

REM 主函数
:main
call :print_info "GentianAphrodite 启动脚本"
call :print_info "=========================="

REM 解析参数
call :parse_args %*

REM 检查环境
call :print_info "检查运行环境..."
call :check_deno
if %errorlevel% neq 0 exit /b %errorlevel%

call :check_config
if %errorlevel% neq 0 exit /b %errorlevel%

call :create_directories

if "%CHECK_ONLY%"=="true" (
    call :print_success "环境检查完成，一切正常！"
    pause
    exit /b 0
)

REM 构建并执行启动命令
call :build_command
call :print_info "启动命令: !CMD!"
call :print_info "正在启动 GentianAphrodite..."

REM 执行命令
!CMD!

if %errorlevel% neq 0 (
    call :print_error "应用程序启动失败，退出码: %errorlevel%"
    pause
    exit /b %errorlevel%
)

goto :eof

REM 运行主函数
call :main %*
