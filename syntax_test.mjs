#!/usr/bin/env deno run --allow-all

/**
 * 语法测试 - 检查文件是否有语法错误
 */

console.log('🧪 语法测试开始...')

try {
	console.log('📋 测试lib/prompt_struct.mjs语法...')
	
	// 尝试解析文件内容
	const content = await Deno.readTextFile('./lib/prompt_struct.mjs')
	console.log('✅ 文件读取成功，长度:', content.length)
	
	// 检查是否有明显的语法问题
	if (content.includes('while (detail_level--) {')) {
		console.log('❌ 发现无限循环问题：while (detail_level--)')
	} else if (content.includes('while (detail_level-- > 0) {')) {
		console.log('✅ 循环条件已修复')
	}
	
	// 尝试动态导入（这会检查语法）
	console.log('📋 尝试动态导入...')
	const module = await import('./lib/prompt_struct.mjs')
	console.log('✅ 模块导入成功')
	
	// 检查导出的函数
	if (typeof module.buildPromptStruct === 'function') {
		console.log('✅ buildPromptStruct函数存在')
	} else {
		console.log('❌ buildPromptStruct函数不存在')
	}
	
	console.log('🎉 语法测试通过！')
	
} catch (error) {
	console.error('❌ 语法测试失败:', error.message)
	console.error('错误堆栈:', error.stack)
}
