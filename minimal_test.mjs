#!/usr/bin/env deno run --allow-all

/**
 * 最小化fount兼容性测试
 */

console.log('🧪 最小化fount兼容性测试...\n')

try {
	console.log('📋 测试: 直接导入和调用buildPromptStruct')
	
	// 直接导入buildPromptStruct
	const module = await import('./lib/prompt_struct.mjs')
	const { buildPromptStruct } = module
	console.log('✅ buildPromptStruct导入成功')
	
	// 最简单的参数
	const args = {
		char_id: 'test',
		UserCharname: 'User',
		Charname: 'Char',
		chat_log: []
	}
	
	console.log('📋 调用buildPromptStruct...')
	const result = await buildPromptStruct(args, 1)
	console.log('✅ buildPromptStruct调用成功')
	
	console.log('📋 验证结果结构...')
	console.log('结果类型:', typeof result)
	console.log('是否有char_id:', 'char_id' in result)
	console.log('是否有UserCharname:', 'UserCharname' in result)
	console.log('是否有Charname:', 'Charname' in result)
	console.log('是否有ReplyToCharname:', 'ReplyToCharname' in result)
	console.log('是否有char_prompt:', 'char_prompt' in result)
	
	if (result.char_id === 'test' && result.UserCharname === 'User' && result.Charname === 'Char') {
		console.log('✅ 基本字段验证通过')
	} else {
		console.log('❌ 基本字段验证失败')
	}
	
	console.log('\n🎉 最小化测试通过！')
	
} catch (error) {
	console.error('❌ 测试失败:', error.message)
	console.error('错误堆栈:', error.stack)
}
