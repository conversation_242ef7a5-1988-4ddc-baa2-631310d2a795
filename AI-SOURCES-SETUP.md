# 🤖 AI 源配置指南

## 📋 AI 源分类系统

GentianAphrodite 使用智能的 AI 源分类系统，根据不同场景自动选择最合适的 AI 源：

### 🧠 AI 源类型（按智商排序）

1. **`detail-thinking`** - 详细思考模型（最高智商）
   - 用于：复杂推理、深度分析、创意写作
   - 优先级：最高，但消耗资源最多

2. **`expert`** - 专家模型
   - 用于：专业问题、技术咨询、学术讨论
   - 优先级：高，平衡性能和质量

3. **`sfw`** - 正经使用模型
   - 用于：日常对话、一般问题、工作场景
   - 优先级：中等，最常用的模型

4. **`web-browse`** - 网页浏览模型
   - 用于：搜索任务、实时信息、网页内容分析
   - 优先级：特定场景优先

5. **`nsfw`** - 成人内容模型
   - 用于：成人对话、情感陪伴
   - 优先级：特定场景使用

6. **`logic`** - 简易逻辑模型
   - 用于：简单判断、快速响应、逻辑推理
   - 优先级：最低，但速度最快

## 🔧 配置方法

### 方法 1：通过 config.json 配置（推荐）

在你的 `config.json` 文件中配置 AI 源：

```json
{
  "ai": {
    "defaultSource": "sfw",
    "maxTokens": 4000,
    "temperature": 0.7,
    "sources": {
      "openai": {
        "enabled": true,
        "apiKey": "sk-your-openai-api-key",
        "model": "gpt-4",
        "endpoint": "https://api.openai.com/v1"
      },
      "anthropic": {
        "enabled": true,
        "apiKey": "sk-ant-your-anthropic-key",
        "model": "claude-3-sonnet-20240229",
        "endpoint": "https://api.anthropic.com"
      },
      "local": {
        "enabled": true,
        "model": "llama2-7b",
        "endpoint": "http://localhost:11434"
      }
    }
  }
}
```

### 方法 2：创建独立的 AI 源配置文件

在 `config/aisources/` 目录下创建配置文件：

#### OpenAI GPT-4 (detail-thinking.json)
```json
{
  "name": "OpenAI GPT-4 详细思考",
  "type": "openai",
  "endpoint": "https://api.openai.com/v1",
  "apiKey": "sk-your-openai-api-key",
  "model": "gpt-4",
  "parameters": {
    "temperature": 0.8,
    "top_p": 0.9,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0
  },
  "maxTokens": 8000,
  "enabled": true,
  "description": "用于复杂推理和创意任务的高级模型"
}
```

#### Claude 3 (expert.json)
```json
{
  "name": "Claude 3 专家模型",
  "type": "anthropic",
  "endpoint": "https://api.anthropic.com",
  "apiKey": "sk-ant-your-anthropic-key",
  "model": "claude-3-sonnet-20240229",
  "parameters": {
    "temperature": 0.7,
    "top_p": 0.9,
    "max_tokens": 4000
  },
  "maxTokens": 4000,
  "enabled": true,
  "description": "专业问题和技术咨询的专家级模型"
}
```

#### 本地模型 (sfw.json)
```json
{
  "name": "本地 Llama2 模型",
  "type": "local",
  "endpoint": "http://localhost:11434",
  "apiKey": "",
  "model": "llama2:7b",
  "parameters": {
    "temperature": 0.7,
    "top_p": 0.9,
    "repeat_penalty": 1.1
  },
  "maxTokens": 2048,
  "enabled": true,
  "description": "本地部署的日常对话模型"
}
```

#### 快速逻辑模型 (logic.json)
```json
{
  "name": "快速逻辑模型",
  "type": "openai",
  "endpoint": "https://api.openai.com/v1",
  "apiKey": "sk-your-openai-api-key",
  "model": "gpt-3.5-turbo",
  "parameters": {
    "temperature": 0.1,
    "top_p": 0.5,
    "max_tokens": 1000
  },
  "maxTokens": 1000,
  "enabled": true,
  "description": "快速响应的逻辑判断模型"
}
```

## 🎯 智能回退机制

系统会根据任务类型自动选择 AI 源，并在失败时智能回退：

### 详细思考任务
```
detail-thinking → expert → sfw → web-browse → nsfw → logic
```

### 专家咨询任务
```
expert → detail-thinking → sfw → web-browse → nsfw → logic
```

### 日常对话任务
```
sfw → expert → detail-thinking → web-browse → nsfw → logic
```

### 网页浏览任务
```
web-browse → detail-thinking → expert → sfw → nsfw → logic
```

### 成人内容任务
```
nsfw → logic → web-browse → sfw → expert → detail-thinking
```

### 逻辑判断任务
```
logic → nsfw → web-browse → sfw → expert → detail-thinking
```

## 🔧 配置步骤

### 1. 创建配置目录
```bash
mkdir -p config/aisources
```

### 2. 创建 AI 源配置文件
根据上面的模板创建对应的 JSON 配置文件。

### 3. 配置 API 密钥
- **OpenAI**: 在 [OpenAI Platform](https://platform.openai.com/api-keys) 获取 API 密钥
- **Anthropic**: 在 [Anthropic Console](https://console.anthropic.com/) 获取 API 密钥
- **本地模型**: 确保本地 API 服务正在运行

### 4. 测试配置
```bash
# 测试 AI 源配置
deno run --allow-all test-ai-sources.mjs
```

## 📊 支持的 AI 源类型

### OpenAI
- **模型**: GPT-4, GPT-3.5-turbo, GPT-4-turbo
- **特点**: 高质量、快速响应、多功能
- **适用**: 所有场景

### Anthropic Claude
- **模型**: Claude-3-opus, Claude-3-sonnet, Claude-3-haiku
- **特点**: 安全、准确、长上下文
- **适用**: 专业咨询、文档分析

### 本地模型
- **模型**: Llama2, Mistral, CodeLlama
- **特点**: 私密、免费、可定制
- **适用**: 隐私敏感场景

### 其他 API
- **支持**: 兼容 OpenAI API 格式的服务
- **例如**: Azure OpenAI, 各种代理服务

## 🎛️ 高级配置

### 动态配置
```javascript
// 在运行时更新 AI 源配置
import { getAISourcesManager } from './lib/AIsources_manager.mjs'

const manager = getAISourcesManager()
await manager.updateAISourceConfig('sfw.json', {
  temperature: 0.8,
  maxTokens: 3000
})
```

### 自定义回退策略
```javascript
// 自定义 AI 源调用顺序
import { GetAISourceCallingOrder } from './AISource/index.mjs'

// 修改回退顺序
const customOrder = ['expert', 'sfw', 'logic']
```

## 🚀 使用示例

### 启动应用
```bash
# 使用配置的 AI 源启动
deno run --allow-all app.mjs --platform telegram
```

### 测试 AI 源
```bash
# 测试所有 AI 源连接
deno run --allow-all scripts/test-ai-sources.mjs

# 测试特定 AI 源
deno run --allow-all scripts/test-ai-source.mjs sfw.json
```

## 💡 最佳实践

1. **分层配置**: 为不同任务配置不同质量的模型
2. **成本控制**: 高质量模型用于重要任务，快速模型用于简单任务
3. **备份策略**: 配置多个 AI 源作为备份
4. **本地优先**: 优先使用本地模型保护隐私
5. **监控使用**: 定期检查 AI 源使用情况和成本

## 🔍 故障排除

### 常见问题
1. **API 密钥错误**: 检查密钥格式和权限
2. **网络连接**: 确保能访问 AI 服务端点
3. **配额限制**: 检查 API 使用配额
4. **模型不存在**: 确认模型名称正确

### 调试命令
```bash
# 启用调试模式
deno run --allow-all app.mjs --platform telegram --debug

# 查看 AI 源状态
deno run --allow-all scripts/ai-source-status.mjs
```

这个配置系统让你可以灵活地管理多个 AI 源，并根据不同场景自动选择最合适的模型！
