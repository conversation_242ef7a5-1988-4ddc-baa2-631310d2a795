import { margePrompt } from '../build.mjs'
import { CoreRulesPrompt } from './corerules.mjs'
import { MasterRecognizePrompt } from './master-recognize.mjs'
import { NullReplayPrompt } from './nullreplay.mjs'
import { OptionsPrompt } from './Options.mjs'
import { PromptReviewerPrompt } from './prompt-reviewer.mjs'
import { SoberPrompt } from './sober.mjs'
import { SOSPrompt } from './sos.mjs'
import { StatusBarPrompt } from './StatusBar.mjs'
/** @typedef {import("../../types/chatLog.mjs").chatLogEntry_t} chatLogEntry_t */
/** @typedef {import("../logical_results/index.mjs").logical_results_t} logical_results_t */

/**
 * @typedef {Object} chatReplyRequest_t
 * @property {chatLogEntry_t[]} chat_log - 聊天日志
 * @property {string} chat_name - 聊天名称
 * @property {string} UserCharname - 用户角色名
 * @property {string} Charname - 角色名
 * @property {string[]} locales - 语言设置
 */
/**
 * @typedef {Object} prompt_struct_t
 * @property {Object[]} messages - 消息列表
 * @property {string} systemPrompt - 系统提示词
 * @property {string} userPrompt - 用户提示词
 * @property {Object[]} context - 上下文
 */

/**
 * @param {chatReplyRequest_t} args
 * @param {logical_results_t} logical_results
 * @param {prompt_struct_t} prompt_struct
 * @param {number} detail_level
 */
export async function SystemPrompt(args, logical_results, prompt_struct, detail_level) {
	const result = []
	result.push(SOSPrompt(args, logical_results, prompt_struct, detail_level))

	if (logical_results.talking_about_prompt_review || logical_results.prompt_input)
		result.push(SoberPrompt(args, logical_results, prompt_struct, detail_level))
	result.push(PromptReviewerPrompt(args, logical_results, prompt_struct, detail_level))

	result.push(StatusBarPrompt(args, logical_results, prompt_struct, detail_level))
	result.push(OptionsPrompt(args, logical_results, prompt_struct, detail_level))

	result.push(CoreRulesPrompt(args, logical_results, prompt_struct, detail_level))

	result.push(MasterRecognizePrompt(args, logical_results, prompt_struct, detail_level))

	result.push(NullReplayPrompt(args, logical_results, prompt_struct, detail_level))

	return margePrompt(...await Promise.all(result))
}
