#!/usr/bin/env deno run --allow-all
/**
 * 快速测试核心模块修复
 */

console.log('🔍 快速测试核心模块修复...')

const tests = [
	{
		name: '定时器库',
		path: './lib/timers.mjs'
	},
	{
		name: '定时器功能',
		path: './reply_gener/functions/timer.mjs'
	},
	{
		name: '主模块',
		path: './main.mjs'
	}
]

let passed = 0
let total = tests.length

for (const test of tests) {
	try {
		console.log(`📦 测试 ${test.name}...`)
		await import(test.path)
		console.log(`✅ ${test.name} 导入成功`)
		passed++
	} catch (error) {
		console.error(`❌ ${test.name} 导入失败:`, error.message)
	}
}

console.log(`\n📊 结果: ${passed}/${total} 通过`)

if (passed === total) {
	console.log('🎉 核心模块修复成功！')
} else {
	console.log('⚠️  仍有问题需要修复')
}
