#!/usr/bin/env deno run --allow-all

/**
 * fount兼容性测试脚本
 * 验证GentianAphrodite项目与fount平台的兼容性
 */

import { buildPromptStruct, structPromptToSingleNoChatLog, margeStructPromptChatLog, structPromptToSingle } from './lib/prompt_struct.mjs'
import { testFountCompatibility, validateFountPromptStruct } from './lib/fount_compat.mjs'
import { GetPrompt } from './prompt/index.mjs'

async function runTests() {
	console.log('🧪 开始fount兼容性测试...\n')
	
	// 测试1: 基本的buildPromptStruct调用
	console.log('📋 测试1: buildPromptStruct基本功能')
	try {
		const args = {
			char_id: 'gentian',
			UserCharname: '测试用户',
			Charname: '龙胆',
			ReplyToCharname: '测试用户',
			chat_log: [
				{ name: '测试用户', content: '你好', role: 'user' },
				{ name: '龙胆', content: '你好！很高兴见到你！', role: 'char' }
			]
		}
		
		const prompt_struct = await buildPromptStruct(args, 3)
		console.log('✅ buildPromptStruct调用成功')
		console.log('📊 prompt_struct结构:', {
			char_id: prompt_struct.char_id,
			UserCharname: prompt_struct.UserCharname,
			ReplyToCharname: prompt_struct.ReplyToCharname,
			Charname: prompt_struct.Charname,
			hasCharPrompt: prompt_struct.char_prompt?.text?.length > 0,
			hasUserPrompt: prompt_struct.user_prompt?.text?.length > 0,
			chatLogLength: prompt_struct.chat_log?.length
		})
		
		// 验证结构
		if (validateFountPromptStruct(prompt_struct)) {
			console.log('✅ prompt_struct结构验证通过')
		} else {
			console.log('❌ prompt_struct结构验证失败')
		}
		
	} catch (error) {
		console.error('❌ 测试1失败:', error.message)
	}
	
	console.log('\n' + '='.repeat(50) + '\n')
	
	// 测试2: fount兼容的GetPrompt调用
	console.log('📋 测试2: fount兼容的GetPrompt调用')
	try {
		const args = {
			char_id: 'gentian',
			UserCharname: '测试用户',
			Charname: '龙胆',
			chat_log: [
				{ name: '测试用户', content: '请介绍一下你自己', role: 'user' }
			]
		}
		
		// 测试fount模式调用：GetPrompt(args, detail_level)
		const prompt = await GetPrompt(args, 3)
		console.log('✅ fount模式GetPrompt调用成功')
		console.log('📊 返回的prompt结构:', {
			hasText: Array.isArray(prompt.text),
			textLength: prompt.text?.length,
			hasAdditionalChatLog: Array.isArray(prompt.additional_chat_log),
			hasExtension: typeof prompt.extension === 'object'
		})
		
	} catch (error) {
		console.error('❌ 测试2失败:', error.message)
	}
	
	console.log('\n' + '='.repeat(50) + '\n')
	
	// 测试3: 转换函数测试
	console.log('📋 测试3: prompt转换函数')
	try {
		const args = {
			char_id: 'gentian',
			UserCharname: '测试用户',
			Charname: '龙胆',
			chat_log: [
				{ name: '测试用户', content: '你好', role: 'user' },
				{ name: '龙胆', content: '你好！', role: 'char' }
			]
		}
		
		const prompt_struct = await buildPromptStruct(args, 3)
		
		// 测试各种转换函数
		const singleNoChatLog = structPromptToSingleNoChatLog(prompt_struct)
		const chatLog = margeStructPromptChatLog(prompt_struct)
		const fullSingle = structPromptToSingle(prompt_struct)
		
		console.log('✅ structPromptToSingleNoChatLog成功')
		console.log('📝 长度:', singleNoChatLog.length, '字符')
		
		console.log('✅ margeStructPromptChatLog成功')
		console.log('📝 聊天日志条数:', chatLog.length)
		
		console.log('✅ structPromptToSingle成功')
		console.log('📝 完整prompt长度:', fullSingle.length, '字符')
		
	} catch (error) {
		console.error('❌ 测试3失败:', error.message)
	}
	
	console.log('\n' + '='.repeat(50) + '\n')
	
	// 测试4: 综合兼容性测试
	console.log('📋 测试4: 综合兼容性测试')
	const compatResult = await testFountCompatibility({
		char_id: 'gentian',
		UserCharname: '测试用户',
		Charname: '龙胆',
		ReplyToCharname: '测试用户'
	})
	
	if (compatResult) {
		console.log('✅ 综合兼容性测试通过')
	} else {
		console.log('❌ 综合兼容性测试失败')
	}
	
	console.log('\n' + '='.repeat(50) + '\n')
	
	// 测试5: GentianAphrodite原生模式测试
	console.log('📋 测试5: GentianAphrodite原生模式')
	try {
		const args = {
			char_id: 'gentian',
			UserCharname: '测试用户',
			Charname: '龙胆',
			chat_log: []
		}
		
		// 先构建prompt_struct
		const prompt_struct = await buildPromptStruct(args, 3)
		
		// 然后使用原生模式调用
		const prompt = await GetPrompt(args, prompt_struct, 3)
		
		console.log('✅ GentianAphrodite原生模式调用成功')
		console.log('📊 返回的prompt结构:', {
			hasText: Array.isArray(prompt.text),
			textLength: prompt.text?.length
		})
		
	} catch (error) {
		console.error('❌ 测试5失败:', error.message)
	}
	
	console.log('\n🎉 所有测试完成！')
}

// 运行测试
runTests().catch(console.error)
