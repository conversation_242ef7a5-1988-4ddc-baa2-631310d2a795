# Telegram 机器人设置指南

本指南将帮助您设置和配置 GentianAphrodite Telegram 机器人。

## 第一步：创建 Telegram 机器人

### 1. 与 BotFather 对话

1. 在 Telegram 中搜索 `@BotFather` 或点击 [这里](https://t.me/botfather)
2. 发送 `/start` 开始对话
3. 发送 `/newbot` 创建新机器人

### 2. 设置机器人信息

1. **机器人名称**: 输入您想要的机器人显示名称（例如：`龙胆机器人`）
2. **机器人用户名**: 输入唯一的用户名，必须以 `bot` 结尾（例如：`gentian_ai_bot`）

### 3. 获取机器人令牌

创建成功后，BotFather 会给您一个机器人令牌，格式类似：
```
1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
```

**⚠️ 重要：请妥善保管这个令牌，不要泄露给他人！**

## 第二步：获取您的用户 ID

### 方法 1：使用 @userinfobot

1. 在 Telegram 中搜索 `@userinfobot`
2. 发送 `/start`
3. 机器人会回复您的用户信息，包括用户 ID

### 方法 2：使用 @getidsbot

1. 在 Telegram 中搜索 `@getidsbot`
2. 发送任意消息
3. 机器人会回复您的用户 ID

### 方法 3：查看消息转发信息

1. 转发您的任意消息给任何机器人
2. 在转发信息中可以看到您的用户 ID

## 第三步：配置应用程序

### 1. 编辑配置文件

打开 `config.json` 文件，找到 `telegram` 部分：

```json
{
  "telegram": {
    "token": "YOUR_TELEGRAM_BOT_TOKEN",
    "botUsername": "your_bot_username",
    "ownerUserId": "YOUR_TELEGRAM_USER_ID",
    "ownerUsername": "YOUR_TELEGRAM_USERNAME",
    "ownerNameKeywords": [
      "主人",
      "master",
      "owner"
    ]
  }
}
```

### 2. 填写配置信息

- `token`: 从 BotFather 获得的机器人令牌
- `botUsername`: 机器人的用户名（不包含 @）
- `ownerUserId`: 您的 Telegram 用户 ID（数字）
- `ownerUsername`: 您的 Telegram 用户名（不包含 @）
- `ownerNameKeywords`: 机器人识别您的关键词列表

### 3. 配置示例

```json
{
  "telegram": {
    "token": "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz",
    "botUsername": "gentian_ai_bot",
    "ownerUserId": "123456789",
    "ownerUsername": "your_username",
    "ownerNameKeywords": [
      "主人",
      "master",
      "Alice"
    ]
  }
}
```

## 第四步：启动机器人

### 使用命令行启动

```bash
# 启动 Telegram 机器人
deno run --allow-all app.mjs --platform telegram

# 或使用启动脚本
./start.sh --platform telegram

# Windows 用户
start.bat /p telegram
```

### 使用配置文件启动

如果在配置文件中设置了 `"platform": "telegram"`，可以直接运行：

```bash
deno run --allow-all app.mjs
```

## 第五步：测试机器人

### 1. 私聊测试

1. 在 Telegram 中搜索您的机器人用户名
2. 发送 `/start` 开始对话
3. 发送任意消息测试回复功能

### 2. 群组测试

1. 将机器人添加到群组
2. 给机器人管理员权限（可选）
3. @机器人或回复机器人消息进行测试

## 机器人功能

### 基本功能

- ✅ 私聊对话
- ✅ 群组聊天（需要 @ 或回复）
- ✅ 文件处理（图片、文档、音频、视频）
- ✅ Markdown 格式支持
- ✅ 消息引用和回复
- ✅ 多语言支持

### 高级功能

- ✅ 角色扮演对话
- ✅ 上下文记忆
- ✅ 文件分析
- ✅ 群组管理（如果有权限）
- ✅ 自定义命令

## 常见问题

### Q: 机器人不回复消息

**A: 检查以下几点：**
1. 机器人令牌是否正确
2. 网络连接是否正常
3. 在群组中是否 @ 了机器人
4. 检查控制台是否有错误信息

### Q: 机器人提示权限错误

**A: 可能的原因：**
1. 机器人令牌无效或过期
2. 机器人被 Telegram 限制
3. 网络防火墙阻止连接

### Q: 机器人在群组中不工作

**A: 确保：**
1. 机器人已被添加到群组
2. 机器人有发送消息的权限
3. 使用 @ 提及机器人或回复机器人消息
4. 群组设置允许机器人发言

### Q: 如何获取群组 ID

**A: 方法：**
1. 将 @getidsbot 添加到群组
2. 发送任意消息
3. 机器人会回复群组 ID

## 安全建议

### 1. 保护机器人令牌

- 不要在公开场所分享令牌
- 定期更换令牌（通过 BotFather）
- 使用环境变量存储敏感信息

### 2. 限制访问权限

```json
{
  "telegram": {
    "allowedUsers": ["123456789", "987654321"],
    "adminUsers": ["123456789"],
    "enableGroupChat": false
  }
}
```

### 3. 监控使用情况

- 定期检查日志文件
- 监控异常活动
- 设置使用限制

## 故障排除

### 启用调试模式

```bash
deno run --allow-all app.mjs --platform telegram --debug
```

### 查看日志

```bash
# 实时查看日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log
```

### 重启机器人

```bash
# 停止机器人（Ctrl+C）
# 然后重新启动
deno run --allow-all app.mjs --platform telegram
```

## 更新和维护

### 更新机器人

1. 备份配置文件
2. 更新应用程序代码
3. 重启机器人服务

### 定期维护

- 清理日志文件
- 更新依赖包
- 备份配置和数据

## 获取帮助

如果遇到问题：

1. 查看控制台输出
2. 检查日志文件
3. 阅读错误信息
4. 提交 Issue 到 GitHub

---

**祝您使用愉快！** 🤖✨
