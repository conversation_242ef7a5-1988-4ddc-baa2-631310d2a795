/**
 * Character API 类型定义
 * 基于 fount 平台的 charAPI_t 接口重新实现
 */

/**
 * 角色信息类型
 * @typedef {Object} CharInfo_t
 * @property {string} name - 角色名称
 * @property {string} description - 角色描述
 * @property {string} version - 版本信息
 * @property {string[]} tags - 标签列表
 * @property {Object} metadata - 元数据
 */

/**
 * 聊天接口类型
 * @typedef {Object} ChatInterface_t
 * @property {Function} GetGreeting - 获取问候语
 * @property {Function} GetGroupGreeting - 获取群组问候语
 * @property {Function} GetPrompt - 获取提示词
 * @property {Function} GetPromptForOther - 为其他角色获取提示词
 * @property {Function} GetReply - 获取回复
 */

/**
 * 配置接口类型
 * @typedef {Object} ConfigInterface_t
 * @property {Function} GetData - 获取配置数据
 * @property {Function} SetData - 设置配置数据
 */

/**
 * 信息接口类型
 * @typedef {Object} InfoInterface_t
 * @property {Function} UpdateInfo - 更新信息
 */

/**
 * Discord 接口类型
 * @typedef {Object} DiscordInterface_t
 * @property {Function} OnceClientReady - 客户端就绪回调
 * @property {Function} GetBotConfigTemplate - 获取机器人配置模板
 */

/**
 * Telegram 接口类型
 * @typedef {Object} TelegramInterface_t
 * @property {Function} BotSetup - 机器人设置
 * @property {Function} GetBotConfigTemplate - 获取机器人配置模板
 */

/**
 * Shell 助手接口类型
 * @typedef {Object} ShellAssistInterface_t
 * @property {Function} Assist - 提供助手功能
 */

/**
 * 定时器接口类型
 * @typedef {Object} TimersInterface_t
 * @property {Function} TimerCallback - 定时器回调
 */

/**
 * 角色接口集合类型
 * @typedef {Object} CharInterfaces_t
 * @property {InfoInterface_t} info - 信息接口
 * @property {ConfigInterface_t} config - 配置接口
 * @property {ChatInterface_t} chat - 聊天接口
 * @property {DiscordInterface_t} discord - Discord 接口
 * @property {TelegramInterface_t} telegram - Telegram 接口
 * @property {ShellAssistInterface_t} shellassist - Shell 助手接口
 * @property {TimersInterface_t} timers - 定时器接口
 */

/**
 * 角色 API 主接口类型
 * @typedef {Object} charAPI_t
 * @property {CharInfo_t} info - 角色信息
 * @property {Function} Init - 初始化函数
 * @property {Function} Load - 加载函数
 * @property {Function} Unload - 卸载函数
 * @property {Function} Uninstall - 卸载函数
 * @property {CharInterfaces_t} interfaces - 接口集合
 */

/**
 * 初始化状态类型
 * @typedef {Object} InitStat_t
 * @property {string} username - 用户名
 * @property {Object} config - 配置对象
 * @property {string} dataDir - 数据目录
 */

/**
 * 卸载原因类型
 * @typedef {'shutdown'|'reload'|'error'|'manual'} UnloadReason_t
 */

/**
 * 卸载来源类型
 * @typedef {'user'|'system'|'admin'} UninstallFrom_t
 */

export {}
