#!/usr/bin/env deno run --allow-all

/**
 * 简单的fount兼容性测试
 */

console.log('🧪 开始简单的fount兼容性测试...\n')

try {
	// 测试基本的buildPromptStruct导入和调用
	console.log('📋 测试1: 导入buildPromptStruct函数')
	const { buildPromptStruct } = await import('./lib/prompt_struct.mjs')
	console.log('✅ buildPromptStruct导入成功')
	
	console.log('📋 测试2: 调用buildPromptStruct函数')
	const args = {
		char_id: 'gentian',
		UserCharname: '测试用户',
		Charname: '龙胆',
		ReplyToCharname: '测试用户',
		chat_log: [
			{ name: '测试用户', content: '你好', role: 'user' },
			{ name: '龙胆', content: '你好！很高兴见到你！', role: 'char' }
		]
	}
	
	const prompt_struct = await buildPromptStruct(args, 3)
	console.log('✅ buildPromptStruct调用成功')
	
	console.log('📋 测试3: 验证返回结构')
	console.log('📊 prompt_struct结构:', {
		char_id: prompt_struct.char_id,
		UserCharname: prompt_struct.UserCharname,
		ReplyToCharname: prompt_struct.ReplyToCharname,
		Charname: prompt_struct.Charname,
		hasCharPrompt: prompt_struct.char_prompt?.text?.length > 0,
		hasUserPrompt: prompt_struct.user_prompt?.text?.length > 0,
		chatLogLength: prompt_struct.chat_log?.length
	})
	
	// 验证必需字段
	const requiredFields = ['char_id', 'UserCharname', 'Charname', 'char_prompt', 'user_prompt', 'world_prompt', 'other_chars_prompt', 'plugin_prompts', 'chat_log']
	let allFieldsPresent = true
	
	for (const field of requiredFields) {
		if (!(field in prompt_struct)) {
			console.error(`❌ 缺少必需字段: ${field}`)
			allFieldsPresent = false
		}
	}
	
	if (allFieldsPresent) {
		console.log('✅ 所有必需字段都存在')
	}
	
	// 验证ReplyToCharname字段
	if (prompt_struct.ReplyToCharname === '测试用户') {
		console.log('✅ ReplyToCharname字段正确设置')
	} else {
		console.log('❌ ReplyToCharname字段设置错误')
	}
	
	console.log('\n📋 测试4: 测试转换函数')
	const { structPromptToSingleNoChatLog, margeStructPromptChatLog, structPromptToSingle } = await import('./lib/prompt_struct.mjs')
	
	const singleNoChatLog = structPromptToSingleNoChatLog(prompt_struct)
	console.log('✅ structPromptToSingleNoChatLog成功')
	console.log('📝 长度:', singleNoChatLog.length, '字符')
	
	const chatLog = margeStructPromptChatLog(prompt_struct)
	console.log('✅ margeStructPromptChatLog成功')
	console.log('📝 聊天日志条数:', chatLog.length)
	
	const fullSingle = structPromptToSingle(prompt_struct)
	console.log('✅ structPromptToSingle成功')
	console.log('📝 完整prompt长度:', fullSingle.length, '字符')
	
	console.log('\n🎉 所有基本测试通过！')
	console.log('\n📋 测试5: 测试fount兼容模式（不依赖其他模块）')
	
	// 测试不同的detail_level值
	for (const level of [0, 1, 2, 3]) {
		const testPromptStruct = await buildPromptStruct(args, level)
		console.log(`✅ detail_level=${level} 测试通过`)
	}
	
	console.log('\n🎉 fount兼容性基本测试全部通过！')
	
} catch (error) {
	console.error('❌ 测试失败:', error.message)
	console.error('错误堆栈:', error.stack)
	Deno.exit(1)
}
