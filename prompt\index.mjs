import { updatePromptTokenData } from '../scripts/statistics.mjs'
import { buildPrompt } from './build.mjs'
import { buildLogicalResults } from './logical_results/index.mjs'
import { buildPromptStruct } from '../lib/prompt_struct.mjs'

/**
 * 获取提示词 - 支持fount兼容模式和GentianAphrodite原生模式
 * @param {Object} args - 参数对象
 * @param {Object|number} [prompt_struct] - 提示词结构（GentianAphrodite模式）或detail_level（fount模式）
 * @param {number} [detail_level] - 详细级别（仅GentianAphrodite模式）
 * @returns {Promise<Object>} 提示词对象
 */
export async function GetPrompt(args, prompt_struct, detail_level) {
	// 检测调用模式
	// fount模式：GetPrompt(args, detail_level) - prompt_struct是数字
	// GentianAphrodite模式：GetPrompt(args, prompt_struct, detail_level) - prompt_struct是对象

	if (typeof prompt_struct === 'number' && detail_level === undefined) {
		// fount兼容模式：prompt_struct实际上是detail_level
		const actualDetailLevel = prompt_struct

		// 使用buildPromptStruct构建prompt_struct，然后调用原有逻辑
		const builtPromptStruct = await buildPromptStruct(args, actualDetailLevel)
		const logical_results = await buildLogicalResults(args, builtPromptStruct, actualDetailLevel)
		const prompt = await buildPrompt(args, logical_results, builtPromptStruct, actualDetailLevel)

		if (actualDetailLevel == 0) {
			updatePromptTokenData(prompt)
		}
		return prompt
	}

	// GentianAphrodite原生模式
	const logical_results = await buildLogicalResults(args, prompt_struct, detail_level)
	const prompt = await buildPrompt(args, logical_results, prompt_struct, detail_level)
	if (detail_level == 0) // 最终回迭代
		updatePromptTokenData(prompt)
	return prompt
}

export async function GetPromptForOther(args, prompt_struct, detail_level) {
	return {
		text: [
			{
				content: `\
龙胆看上去年幼清纯却身材丰满，身高约在130cm上下。可谓“细腰丰乳小翘臀，棕黑长发双马尾”。
粉紫双眼，M cup，日常穿着粉色点缀的黑色哥特萝莉短裙和黑丝袜。
`,
				important: 0
			}
		],
	}
}
