/**
 * 本地化和语言处理工具
 * 本地实现，替代 fount 平台的 locale 脚本
 */

import fs from 'node:fs'
import path from 'node:path'
import { loadJsonFileIfExists } from './json_loader.mjs'

/**
 * 支持的语言列表
 */
export const supportedLocales = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR']

/**
 * 默认语言
 */
export const defaultLocale = 'zh-CN'

/**
 * 当前语言设置
 */
let currentLocale = defaultLocale

/**
 * 翻译数据缓存
 */
const translations = new Map()

/**
 * 语言包目录
 */
const localeDir = './locales'

/**
 * 设置当前语言
 * @param {string} locale - 语言代码
 */
export function setLocale(locale) {
	if (supportedLocales.includes(locale)) {
		currentLocale = locale
	} else {
		console.warn(`Unsupported locale: ${locale}, using default: ${defaultLocale}`)
		currentLocale = defaultLocale
	}
}

/**
 * 获取当前语言
 * @returns {string} 当前语言代码
 */
export function getCurrentLocale() {
	return currentLocale
}

/**
 * 加载语言包
 * @param {string} locale - 语言代码
 * @returns {Object} 翻译数据
 */
export function loadLocale(locale) {
	if (translations.has(locale)) {
		return translations.get(locale)
	}

	try {
		const localePath = path.join(localeDir, `${locale}.json`)
		const data = loadJsonFileIfExists(localePath, {})
		translations.set(locale, data)
		return data
	} catch (error) {
		console.warn(`Failed to load locale ${locale}:`, error.message)
		return {}
	}
}

/**
 * 获取翻译文本
 * @param {string} key - 翻译键
 * @param {string} locale - 语言代码，默认使用当前语言
 * @param {Object} params - 参数对象
 * @returns {string} 翻译后的文本
 */
export function t(key, locale = currentLocale, params = {}) {
	const translations = loadLocale(locale)
	
	// 支持嵌套键，如 'user.name'
	const keys = key.split('.')
	let value = translations
	
	for (const k of keys) {
		if (value && typeof value === 'object' && k in value) {
			value = value[k]
		} else {
			// 如果找不到翻译，尝试使用默认语言
			if (locale !== defaultLocale) {
				return t(key, defaultLocale, params)
			}
			// 如果默认语言也找不到，返回键本身
			return key
		}
	}
	
	// 如果值不是字符串，返回键
	if (typeof value !== 'string') {
		return key
	}
	
	// 替换参数
	return value.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
		return params[paramKey] !== undefined ? params[paramKey] : match
	})
}

/**
 * 获取部分信息（AI 源相关）
 * @param {Object} source - AI 源对象
 * @returns {Promise<Object>} 部分信息
 */
export async function getPartInfo(source) {
	if (!source) {
		return { name: 'Unknown' }
	}

	// 如果有文件名，尝试从中提取信息
	if (source.filename) {
		const baseName = path.basename(source.filename, path.extname(source.filename))
		return {
			name: source.name || baseName,
			filename: source.filename,
			description: source.description || '',
			type: source.config?.type || 'unknown'
		}
	}

	// 如果有名称，直接使用
	if (source.name) {
		return {
			name: source.name,
			description: source.description || '',
			type: source.type || 'unknown'
		}
	}

	// 默认返回
	return { name: 'Unknown' }
}

/**
 * 格式化日期时间
 * @param {Date|number} date - 日期对象或时间戳
 * @param {string} locale - 语言代码
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(date, locale = currentLocale, options = {}) {
	const dateObj = date instanceof Date ? date : new Date(date)
	
	const defaultOptions = {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
		second: '2-digit'
	}
	
	const formatOptions = { ...defaultOptions, ...options }
	
	try {
		return dateObj.toLocaleString(locale, formatOptions)
	} catch (error) {
		// 如果指定语言格式化失败，使用默认语言
		return dateObj.toLocaleString(defaultLocale, formatOptions)
	}
}

/**
 * 格式化数字
 * @param {number} number - 数字
 * @param {string} locale - 语言代码
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的数字
 */
export function formatNumber(number, locale = currentLocale, options = {}) {
	try {
		return number.toLocaleString(locale, options)
	} catch (error) {
		return number.toLocaleString(defaultLocale, options)
	}
}

/**
 * 获取语言的显示名称
 * @param {string} locale - 语言代码
 * @param {string} displayLocale - 显示语言代码
 * @returns {string} 语言显示名称
 */
export function getLocaleDisplayName(locale, displayLocale = currentLocale) {
	const displayNames = {
		'zh-CN': {
			'zh-CN': '简体中文',
			'en-US': 'English',
			'ja-JP': '日本語',
			'ko-KR': '한국어'
		},
		'en-US': {
			'zh-CN': 'Chinese (Simplified)',
			'en-US': 'English',
			'ja-JP': 'Japanese',
			'ko-KR': 'Korean'
		}
	}
	
	return displayNames[displayLocale]?.[locale] || locale
}

/**
 * 检测文本语言
 * @param {string} text - 文本内容
 * @returns {string} 检测到的语言代码
 */
export function detectLanguage(text) {
	// 简单的语言检测逻辑
	const chineseRegex = /[\u4e00-\u9fff]/
	const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/
	const koreanRegex = /[\uac00-\ud7af]/
	
	if (chineseRegex.test(text)) {
		return 'zh-CN'
	}
	if (japaneseRegex.test(text)) {
		return 'ja-JP'
	}
	if (koreanRegex.test(text)) {
		return 'ko-KR'
	}
	
	// 默认返回英语
	return 'en-US'
}

/**
 * 初始化本地化系统
 * @param {string} locale - 初始语言
 */
export function initLocale(locale = defaultLocale) {
	setLocale(locale)
	
	// 确保语言包目录存在
	if (!fs.existsSync(localeDir)) {
		fs.mkdirSync(localeDir, { recursive: true })
	}
	
	// 预加载当前语言包
	loadLocale(currentLocale)
	
	console.log(`Locale system initialized with language: ${currentLocale}`)
}

// 自动初始化
initLocale()
