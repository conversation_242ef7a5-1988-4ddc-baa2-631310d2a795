#!/usr/bin/env deno run --allow-all
/**
 * 测试真实AI调用功能
 */

import aiSourcesManager from './lib/AIsources_manager.mjs'
import { initLocale } from './lib/locale.mjs'
import { initTranslations } from './lib/i18n.mjs'

// 初始化本地化
initLocale('zh-CN')
initTranslations('default', 'zh-CN')

console.log('🧪 测试真实AI调用功能')
console.log('==================================================')

async function testAICall() {
    try {
        // 使用AI源管理器实例
        const aiManager = aiSourcesManager

        // 加载AI源
        console.log('📁 加载AI源配置...')
        await aiManager.loadAISources('./config/aisources')
        
        // 获取可用的AI源
        const sources = aiManager.listAISources()
        console.log(`✅ 发现 ${sources.length} 个AI源`)
        
        for (const source of sources) {
            if (!source.config.enabled) {
                console.log(`⏭️  跳过已禁用的AI源: ${source.filename}`)
                continue
            }
            
            console.log(`\n🤖 测试AI源: ${source.filename} (${source.config.name})`)
            console.log(`   类型: ${source.config.type}`)
            console.log(`   模型: ${source.config.model}`)
            console.log(`   端点: ${source.config.endpoint}`)
            
            try {
                // 测试简单的消息调用
                const testMessages = [
                    {
                        role: 'user',
                        content: '你好，请简单回复一下确认你能正常工作。'
                    }
                ]
                
                console.log('   📤 发送测试消息...')
                const startTime = Date.now()
                
                const response = await aiManager.performAICallWithMessages(source, testMessages)
                
                const endTime = Date.now()
                const duration = endTime - startTime
                
                console.log(`   ✅ 调用成功 (耗时: ${duration}ms)`)
                console.log(`   📥 AI回复: ${response.content.substring(0, 100)}${response.content.length > 100 ? '...' : ''}`)
                
                if (response.files && response.files.length > 0) {
                    console.log(`   📎 附件数量: ${response.files.length}`)
                }
                
            } catch (error) {
                console.log(`   ❌ 调用失败: ${error.message}`)
                
                // 显示详细错误信息（仅在调试模式下）
                if (globalThis.DEBUG) {
                    console.error('   详细错误:', error)
                }
            }
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message)
        if (globalThis.DEBUG) {
            console.error(error.stack)
        }
    }
}

// 运行测试
if (import.meta.main) {
    // 启用调试模式
    globalThis.DEBUG = true
    
    await testAICall()
    
    console.log('\n==================================================')
    console.log('🎯 测试完成')
}
