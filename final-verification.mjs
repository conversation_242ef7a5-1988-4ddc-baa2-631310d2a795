#!/usr/bin/env deno run --allow-all
/**
 * 最终验证脚本 - 验证所有模块引用修复
 */

console.log('🔍 GentianAphrodite 模块引用修复最终验证')
console.log('=' .repeat(50))

let totalTests = 0
let passedTests = 0

async function testModule(name, path, expectedExports = []) {
	totalTests++
	try {
		console.log(`📦 测试 ${name}...`)
		const module = await import(path)
		
		// 检查导出的函数是否存在
		for (const exportName of expectedExports) {
			if (typeof module[exportName] === 'undefined') {
				throw new Error(`缺少导出: ${exportName}`)
			}
		}
		
		console.log(`✅ ${name} 导入成功`)
		passedTests++
		return true
	} catch (error) {
		console.error(`❌ ${name} 导入失败:`, error.message)
		return false
	}
}

// 核心模块测试
console.log('\n🔧 核心模块测试')
await testModule('定时器库模块', './lib/timers.mjs', ['getTimers', 'setTimer', 'removeTimer', 'initTimers'])
await testModule('主模块', './main.mjs', ['default'])
await testModule('配置模块', './config.mjs', ['GetData', 'SetData'])

// 定时器相关模块测试
console.log('\n⏰ 定时器相关模块测试')
await testModule('定时器功能模块', './reply_gener/functions/timer.mjs', ['timer', 'timerCallBack'])
await testModule('回复生成器主模块', './reply_gener/index.mjs', ['GetReply'])

// 提示词系统测试
console.log('\n💬 提示词系统测试')
await testModule('提示词函数索引', './prompt/functions/index.mjs', ['GetPromptFunctions'])
await testModule('定时器提示词', './prompt/functions/timer.mjs', ['TimerPrompt'])

// 脚本模块测试
console.log('\n📜 脚本模块测试')
await testModule('变量管理', './scripts/vars.mjs', ['getVar', 'saveVar', 'saveVars'])
await testModule('工具函数', './scripts/tools.mjs', ['parseDuration', 'timeToStr'])

// 接口模块测试
console.log('\n🔌 接口模块测试')
await testModule('Telegram 接口', './interfaces/telegram/index.mjs', ['TelegramBotMain'])

// 类型定义测试
console.log('\n📋 类型定义测试')
await testModule('聊天日志类型', './types/chatLog.mjs', ['default'])
await testModule('角色API类型', './types/charAPI.mjs', ['default'])

// 功能测试
console.log('\n🧪 功能测试')
try {
	console.log('📦 测试定时器系统初始化...')
	const { initTimers, getTimers, setTimer, removeTimer } = await import('./lib/timers.mjs')
	
	// 初始化定时器系统
	await initTimers()
	console.log('✅ 定时器系统初始化成功')
	
	// 测试基本功能
	const emptyTimers = await getTimers('test_user', 'test_category', 'test_id')
	if (typeof emptyTimers === 'object') {
		console.log('✅ 定时器获取功能正常')
		passedTests++
	} else {
		console.log('❌ 定时器获取功能异常')
	}
	totalTests++
	
} catch (error) {
	console.error('❌ 定时器功能测试失败:', error.message)
	totalTests++
}

// 总结
console.log('\n' + '='.repeat(50))
console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`)

if (passedTests === totalTests) {
	console.log('🎉 所有模块引用修复成功！')
	console.log('')
	console.log('✅ 核心问题已解决：')
	console.log('  - timers.mjs 模块已创建并正常工作')
	console.log('  - 所有错误的 fount 平台引用已修复')
	console.log('  - 类型定义已标准化为本地 JavaScript 类型')
	console.log('  - Windows 兼容性问题已解决')
	console.log('')
	console.log('🚀 下一步：')
	console.log('  1. 运行: deno run --allow-all verify-telegram.mjs')
	console.log('  2. 配置 config.json 中的 Telegram 设置')
	console.log('  3. 启动应用: deno run --allow-all app.mjs --platform telegram')
} else {
	console.log('⚠️  仍有模块引用问题需要修复')
	console.log('')
	console.log('❌ 失败的测试需要进一步检查')
	Deno.exit(1)
}

console.log('')
