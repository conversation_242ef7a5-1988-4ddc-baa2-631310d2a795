#!/usr/bin/env deno run --allow-all
/**
 * 测试 AI 源修复
 */

console.log('🔧 测试 AI 源修复...')

try {
	// 测试 AI 源初始化
	console.log('📦 测试 AI 源初始化...')
	const { initAISources, AIsources, noAISourceAvailable } = await import('./AISource/index.mjs')
	
	// 初始化 AI 源
	await initAISources()
	
	// 检查 AI 源状态
	console.log('\n📊 AI 源状态:')
	for (const [type, source] of Object.entries(AIsources)) {
		if (source) {
			console.log(`✅ ${type}: ${source.name} (${source.filename})`)
		} else {
			console.log(`❌ ${type}: 未配置`)
		}
	}
	
	// 检查是否有可用的 AI 源
	const hasAISource = !noAISourceAvailable()
	console.log(`\n🤖 AI 源可用性: ${hasAISource ? '✅ 有可用的 AI 源' : '❌ 没有可用的 AI 源'}`)
	
	if (hasAISource) {
		console.log('🎉 AI 源修复成功！')
		
		// 测试主模块加载
		console.log('\n📦 测试主模块加载...')
		const GentianAphrodite = await import('./main.mjs')
		console.log('✅ 主模块加载成功')
		
		// 测试初始化
		console.log('\n🔧 测试应用程序初始化...')
		const initStat = {
			username: 'TestUser',
			config: {},
			dataDir: './data'
		}
		
		if (GentianAphrodite.default.Init) {
			await GentianAphrodite.default.Init(initStat)
			console.log('✅ 应用程序初始化成功')
		}
		
		// 再次检查 AI 源状态
		console.log('\n📊 初始化后 AI 源状态:')
		const { AIsources: updatedSources } = await import('./AISource/index.mjs')
		for (const [type, source] of Object.entries(updatedSources)) {
			if (source) {
				console.log(`✅ ${type}: ${source.name}`)
			} else {
				console.log(`❌ ${type}: 未配置`)
			}
		}
		
		console.log('\n🎉 所有测试通过！AI 源修复成功！')
		console.log('\n📝 下一步:')
		console.log('1. 启动 Telegram 机器人: deno run --allow-all app.mjs --platform telegram')
		console.log('2. 在 Telegram 中测试对话功能')
		
	} else {
		console.log('\n⚠️  没有找到可用的 AI 源')
		console.log('请检查 config/aisources/ 目录下的配置文件')
		console.log('确保至少有一个配置文件的 enabled 设为 true')
	}
	
} catch (error) {
	console.error('❌ 测试失败:', error.message)
	if (globalThis.DEBUG) {
		console.error(error.stack)
	}
	Deno.exit(1)
}
