import { decodeQrCode<PERSON><PERSON><PERSON>uffer } from '../../scripts/qrcode.mjs'
/** @typedef {import("../../types/chatLog.mjs").chatLogEntry_t} chatLogEntry_t */
/** @typedef {import("../logical_results/index.mjs").logical_results_t} logical_results_t */

/**
 * @typedef {Object} chatReplyRequest_t
 * @property {chatLogEntry_t[]} chat_log - 聊天日志
 * @property {string} chat_name - 聊天名称
 * @property {string} UserCharname - 用户角色名
 * @property {string} Charname - 角色名
 * @property {string} char_id - 角色ID
 */

/**
 * @typedef {Object} prompt_struct_t
 * @property {Object[]} messages - 消息列表
 * @property {string} systemPrompt - 系统提示词
 * @property {string} userPrompt - 用户提示词
 * @property {Object[]} context - 上下文
 */

/**
 * @param {chatReplyRequest_t} args
 * @param {logical_results_t} logical_results
 * @param {prompt_struct_t} prompt_struct
 * @param {number} detail_level
 */
export async function qrcodeParserPrompt(args, logical_results, prompt_struct, detail_level) {
	const logs = args.chat_log.slice(-20)

	for (const log of logs) {
		if (log.extension?.decodedQRCodes) continue
		const imgs = (log.files || []).filter(x => x.mimeType.startsWith('image/'))
		const qrcodes = (await Promise.all(
			imgs.map(img => decodeQrCodeFromBuffer(img.buffer))
		)).filter(arr => arr.length)

		if (qrcodes.length) {
			log.extension.decodedQRCodes = qrcodes
			log.logContextAfter ??= []
			log.logContextAfter.push({
				name: 'system',
				role: 'system',
				content: `\
上条消息中图片内的二维码内容是：
${qrcodes.map(x => x.join('\n')).join('\n')}
`,
				charVisibility: [args.char_id]
			})
		}
	}

	return {
		text: [],
		additional_chat_log: []
	}
}
