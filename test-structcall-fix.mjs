#!/usr/bin/env deno run --allow-all
/**
 * 测试 StructCall 修复
 * 验证 AI 源的 StructCall 方法是否正常工作
 */

import { initAISources, AIsources, OrderedAISourceCalling } from './AISource/index.mjs'
import { buildPromptStruct } from './lib/prompt_struct.mjs'
import { initLocale } from './lib/locale.mjs'
import { initTranslations } from './lib/i18n.mjs'

console.log('🧪 开始测试 StructCall 修复...')

// 初始化本地化
initLocale('zh-CN')
initTranslations('default', 'zh-CN')

// 初始化 AI 源
console.log('📋 初始化 AI 源...')
await initAISources()

// 检查 AI 源状态
console.log('\n📊 AI 源状态:')
for (const [name, source] of Object.entries(AIsources)) {
	if (source) {
		console.log(`✅ ${name}: ${source.name} (${source.type})`)
		console.log(`   - StructCall 方法: ${typeof source.StructCall}`)
		console.log(`   - Call 方法: ${typeof source.Call}`)
		console.log(`   - Tokenizer: ${typeof source.Tokenizer}`)
	} else {
		console.log(`❌ ${name}: 未加载`)
	}
}

// 创建测试用的提示词结构
console.log('\n🔧 构建测试提示词结构...')
const testArgs = {
	char_id: 'gentian',
	Charname: '龙胆',
	UserCharname: '测试用户',
	chat_log: [
		{
			name: '测试用户',
			role: 'user',
			content: '你好，请介绍一下你自己',
			time: Date.now()
		}
	]
}

const prompt_struct = await buildPromptStruct(testArgs)
console.log('✅ 提示词结构构建完成')
console.log(`   - 角色ID: ${prompt_struct.char_id}`)
console.log(`   - 角色名: ${prompt_struct.Charname}`)
console.log(`   - 用户名: ${prompt_struct.UserCharname}`)
console.log(`   - 聊天日志条数: ${prompt_struct.chat_log.length}`)
console.log(`   - 角色提示词条数: ${prompt_struct.char_prompt.text.length}`)

// 测试 StructCall 方法
console.log('\n🚀 测试 StructCall 方法...')

try {
	const result = await OrderedAISourceCalling('sfw', async (AI) => {
		console.log(`📞 调用 AI 源: ${AI.name}`)
		console.log(`   - StructCall 类型: ${typeof AI.StructCall}`)
		
		if (typeof AI.StructCall !== 'function') {
			throw new Error(`StructCall 不是函数: ${typeof AI.StructCall}`)
		}
		
		const response = await AI.StructCall(prompt_struct)
		console.log(`✅ StructCall 调用成功`)
		console.log(`   - 响应类型: ${typeof response}`)
		console.log(`   - 响应内容: ${response?.content?.substring(0, 100)}...`)
		
		return response
	})
	
	console.log('\n🎉 StructCall 测试成功！')
	console.log(`📝 AI 回复: ${result.content}`)
	
} catch (error) {
	console.error('\n❌ StructCall 测试失败:')
	console.error(`错误信息: ${error.message}`)
	console.error(`错误堆栈: ${error.stack}`)
}

// 测试单个 AI 源的 StructCall
console.log('\n🔍 测试单个 AI 源的 StructCall...')
for (const [name, source] of Object.entries(AIsources)) {
	if (source && typeof source.StructCall === 'function') {
		try {
			console.log(`\n📞 测试 ${name} (${source.name})...`)
			const response = await source.StructCall(prompt_struct)
			console.log(`✅ ${name} StructCall 成功`)
			console.log(`   - 响应: ${response?.content?.substring(0, 50)}...`)
		} catch (error) {
			console.error(`❌ ${name} StructCall 失败: ${error.message}`)
		}
	}
}

console.log('\n✨ 测试完成！')
