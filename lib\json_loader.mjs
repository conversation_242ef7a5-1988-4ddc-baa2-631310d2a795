/**
 * JSON 文件加载和保存工具
 * 本地实现，替代 fount 平台的 json_loader
 */

import fs from 'node:fs'
import path from 'node:path'

/**
 * 如果文件存在则加载 JSON 文件，否则返回默认值
 * @param {string} filePath - 文件路径
 * @param {any} defaultValue - 默认值
 * @returns {any} 解析后的 JSON 数据或默认值
 */
export function loadJsonFileIfExists(filePath, defaultValue = null) {
	try {
		if (fs.existsSync(filePath)) {
			const content = fs.readFileSync(filePath, 'utf-8')
			return JSON.parse(content)
		}
		return defaultValue
	} catch (error) {
		console.warn(`Failed to load JSON file ${filePath}:`, error.message)
		return defaultValue
	}
}

/**
 * 保存数据到 JSON 文件
 * @param {string} filePath - 文件路径
 * @param {any} data - 要保存的数据
 * @param {Object} options - 选项
 * @param {number} options.indent - 缩进空格数，默认为 2
 * @returns {boolean} 是否保存成功
 */
export function saveJsonFile(filePath, data, options = {}) {
	try {
		const { indent = 2 } = options
		
		// 确保目录存在
		const dir = path.dirname(filePath)
		if (!fs.existsSync(dir)) {
			fs.mkdirSync(dir, { recursive: true })
		}
		
		const jsonString = JSON.stringify(data, null, indent)
		fs.writeFileSync(filePath, jsonString, 'utf-8')
		return true
	} catch (error) {
		console.error(`Failed to save JSON file ${filePath}:`, error.message)
		return false
	}
}

/**
 * 异步加载 JSON 文件
 * @param {string} filePath - 文件路径
 * @param {any} defaultValue - 默认值
 * @returns {Promise<any>} 解析后的 JSON 数据或默认值
 */
export async function loadJsonFileAsync(filePath, defaultValue = null) {
	try {
		const content = await fs.promises.readFile(filePath, 'utf-8')
		return JSON.parse(content)
	} catch (error) {
		if (error.code === 'ENOENT') {
			return defaultValue
		}
		console.warn(`Failed to load JSON file ${filePath}:`, error.message)
		return defaultValue
	}
}

/**
 * 异步保存数据到 JSON 文件
 * @param {string} filePath - 文件路径
 * @param {any} data - 要保存的数据
 * @param {Object} options - 选项
 * @param {number} options.indent - 缩进空格数，默认为 2
 * @returns {Promise<boolean>} 是否保存成功
 */
export async function saveJsonFileAsync(filePath, data, options = {}) {
	try {
		const { indent = 2 } = options
		
		// 确保目录存在
		const dir = path.dirname(filePath)
		await fs.promises.mkdir(dir, { recursive: true })
		
		const jsonString = JSON.stringify(data, null, indent)
		await fs.promises.writeFile(filePath, jsonString, 'utf-8')
		return true
	} catch (error) {
		console.error(`Failed to save JSON file ${filePath}:`, error.message)
		return false
	}
}

/**
 * 检查 JSON 文件是否存在且有效
 * @param {string} filePath - 文件路径
 * @returns {boolean} 文件是否存在且为有效 JSON
 */
export function isValidJsonFile(filePath) {
	try {
		if (!fs.existsSync(filePath)) {
			return false
		}
		const content = fs.readFileSync(filePath, 'utf-8')
		JSON.parse(content)
		return true
	} catch {
		return false
	}
}

/**
 * 备份 JSON 文件
 * @param {string} filePath - 原文件路径
 * @param {string} backupSuffix - 备份后缀，默认为时间戳
 * @returns {string|null} 备份文件路径，失败时返回 null
 */
export function backupJsonFile(filePath, backupSuffix = null) {
	try {
		if (!fs.existsSync(filePath)) {
			return null
		}
		
		const suffix = backupSuffix || new Date().toISOString().replace(/[:.]/g, '-')
		const backupPath = `${filePath}.backup.${suffix}`
		
		fs.copyFileSync(filePath, backupPath)
		return backupPath
	} catch (error) {
		console.error(`Failed to backup JSON file ${filePath}:`, error.message)
		return null
	}
}
