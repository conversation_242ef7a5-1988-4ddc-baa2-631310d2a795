#!/usr/bin/env deno run --allow-all

/**
 * 基础fount兼容性测试 - 只测试核心功能
 */

console.log('🧪 开始基础fount兼容性测试...\n')

try {
	// 测试1: buildPromptStruct基本功能
	console.log('📋 测试1: buildPromptStruct基本功能')
	const { buildPromptStruct, structPromptToSingleNoChatLog, margeStructPromptChatLog, structPromptToSingle } = await import('./lib/prompt_struct.mjs')
	
	const args = {
		char_id: 'gentian',
		UserCharname: '测试用户',
		Charname: '龙胆',
		ReplyToCharname: '测试用户',
		chat_log: [
			{ name: '测试用户', content: '你好', role: 'user' },
			{ name: '龙胆', content: '你好！很高兴见到你！', role: 'char' }
		]
	}
	
	const prompt_struct = await buildPromptStruct(args, 3)
	console.log('✅ buildPromptStruct调用成功')
	
	// 验证fount兼容的结构
	const requiredFields = ['char_id', 'UserCharname', 'ReplyToCharname', 'Charname', 'char_prompt', 'user_prompt', 'world_prompt', 'other_chars_prompt', 'plugin_prompts', 'chat_log']
	let allFieldsPresent = true
	
	for (const field of requiredFields) {
		if (!(field in prompt_struct)) {
			console.error(`❌ 缺少必需字段: ${field}`)
			allFieldsPresent = false
		}
	}
	
	if (allFieldsPresent) {
		console.log('✅ 所有fount必需字段都存在')
	}
	
	// 测试2: 验证ReplyToCharname字段（fount特有）
	console.log('\n📋 测试2: 验证fount特有字段')
	if (prompt_struct.ReplyToCharname === '测试用户') {
		console.log('✅ ReplyToCharname字段正确设置')
	} else {
		console.log('❌ ReplyToCharname字段设置错误')
	}
	
	// 测试3: 验证prompt结构
	console.log('\n📋 测试3: 验证prompt结构')
	if (prompt_struct.char_prompt && prompt_struct.char_prompt.text && Array.isArray(prompt_struct.char_prompt.text)) {
		console.log('✅ char_prompt结构正确')
		console.log(`📝 char_prompt包含 ${prompt_struct.char_prompt.text.length} 个文本项`)
	} else {
		console.log('❌ char_prompt结构错误')
	}
	
	if (prompt_struct.user_prompt && prompt_struct.user_prompt.text && Array.isArray(prompt_struct.user_prompt.text)) {
		console.log('✅ user_prompt结构正确')
		console.log(`📝 user_prompt包含 ${prompt_struct.user_prompt.text.length} 个文本项`)
	} else {
		console.log('❌ user_prompt结构错误')
	}
	
	// 测试4: 测试转换函数
	console.log('\n📋 测试4: 测试fount转换函数')
	
	const singleNoChatLog = structPromptToSingleNoChatLog(prompt_struct)
	console.log('✅ structPromptToSingleNoChatLog成功')
	console.log(`📝 输出长度: ${singleNoChatLog.length} 字符`)
	
	const chatLog = margeStructPromptChatLog(prompt_struct)
	console.log('✅ margeStructPromptChatLog成功')
	console.log(`📝 聊天日志条数: ${chatLog.length}`)
	
	const fullSingle = structPromptToSingle(prompt_struct)
	console.log('✅ structPromptToSingle成功')
	console.log(`📝 完整prompt长度: ${fullSingle.length} 字符`)
	
	// 测试5: 测试detail_level迭代机制
	console.log('\n📋 测试5: 测试detail_level迭代机制')
	
	// 测试不同的detail_level值
	for (const level of [0, 1, 2, 3, 5]) {
		const testPromptStruct = await buildPromptStruct(args, level)
		console.log(`✅ detail_level=${level} 测试通过`)
		
		// 验证结构一致性
		if (testPromptStruct.char_id === args.char_id && 
			testPromptStruct.UserCharname === args.UserCharname &&
			testPromptStruct.ReplyToCharname === args.ReplyToCharname) {
			console.log(`  ✓ detail_level=${level} 结构一致性验证通过`)
		} else {
			console.log(`  ❌ detail_level=${level} 结构一致性验证失败`)
		}
	}
	
	// 测试6: 测试带有组件的迭代（模拟fount组件）
	console.log('\n📋 测试6: 测试带有组件的迭代')
	
	// 创建模拟的fount组件
	const mockChar = {
		interfaces: {
			chat: {
				GetPrompt: async (args, prompt_struct, detail_level) => {
					return {
						text: [{
							content: '这是来自模拟角色组件的提示词',
							description: 'mock_char_prompt',
							important: 10
						}],
						additional_chat_log: [],
						extension: {}
					}
				}
			}
		}
	}
	
	const mockUser = {
		interfaces: {
			chat: {
				GetPrompt: async (args, prompt_struct, detail_level) => {
					return {
						text: [{
							content: '这是来自模拟用户组件的提示词',
							description: 'mock_user_prompt',
							important: 5
						}],
						additional_chat_log: [],
						extension: {}
					}
				}
			}
		}
	}
	
	const argsWithComponents = {
		...args,
		char: mockChar,
		user: mockUser
	}
	
	const promptStructWithComponents = await buildPromptStruct(argsWithComponents, 2)
	console.log('✅ 带组件的buildPromptStruct调用成功')
	
	// 验证组件提示词是否被正确合并
	const charPromptTexts = promptStructWithComponents.char_prompt.text.map(t => t.content)
	const userPromptTexts = promptStructWithComponents.user_prompt.text.map(t => t.content)
	
	if (charPromptTexts.some(text => text.includes('模拟角色组件'))) {
		console.log('✅ 角色组件提示词正确合并')
	} else {
		console.log('❌ 角色组件提示词合并失败')
	}
	
	if (userPromptTexts.some(text => text.includes('模拟用户组件'))) {
		console.log('✅ 用户组件提示词正确合并')
	} else {
		console.log('❌ 用户组件提示词合并失败')
	}
	
	console.log('\n🎉 所有基础fount兼容性测试通过！')
	console.log('\n📊 测试总结:')
	console.log('  ✅ buildPromptStruct函数完全兼容fount调用方式')
	console.log('  ✅ 支持ReplyToCharname字段（fount特有）')
	console.log('  ✅ prompt_struct结构符合fount标准')
	console.log('  ✅ 转换函数工作正常')
	console.log('  ✅ detail_level迭代机制工作正常')
	console.log('  ✅ 组件接口兼容性良好')
	
} catch (error) {
	console.error('❌ 测试失败:', error.message)
	console.error('错误堆栈:', error.stack)
	Deno.exit(1)
}
