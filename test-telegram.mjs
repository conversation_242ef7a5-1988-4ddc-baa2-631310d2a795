#!/usr/bin/env deno run --allow-all
/**
 * Telegram 机器人功能测试脚本
 */

console.log('🤖 GentianAphrodite Telegram 机器人测试')
console.log('=====================================')

// 测试基本导入
try {
	console.log('✓ 测试基本模块导入...')
	
	// 测试主模块
	const GentianAphrodite = await import('./main.mjs')
	console.log('✓ 主模块导入成功')
	
	// 测试 Telegram 接口
	const telegramModule = await import('./interfaces/telegram/index.mjs')
	console.log('✓ Telegram 接口模块导入成功')
	
	// 测试配置模板
	const configTemplate = await GentianAphrodite.default.interfaces.telegram.GetBotConfigTemplate()
	console.log('✓ 配置模板获取成功:', configTemplate)
	
} catch (error) {
	console.error('✗ 模块导入失败:', error.message)
	Deno.exit(1)
}

// 测试 Telegraf 导入
try {
	console.log('✓ 测试 Telegraf 库导入...')
	const { Telegraf } = await import('npm:telegraf')
	console.log('✓ Telegraf 库导入成功')
	
	// 创建测试机器人实例（不启动）
	const testBot = new Telegraf('TEST_TOKEN')
	console.log('✓ Telegraf 实例创建成功')
	
} catch (error) {
	console.error('✗ Telegraf 库导入失败:', error.message)
	console.error('请确保网络连接正常，Deno 可以访问 npm 包')
}

// 测试配置文件解析
try {
	console.log('✓ 测试配置文件解析...')
	
	const configText = await Deno.readTextFile('./config.test.json')
	const config = JSON.parse(configText)
	
	console.log('✓ 配置文件解析成功')
	console.log('  - 平台:', config.platform)
	console.log('  - Telegram 令牌:', config.telegram.token ? '已设置' : '未设置')
	console.log('  - 主人用户ID:', config.telegram.ownerUserId)
	
} catch (error) {
	console.error('✗ 配置文件解析失败:', error.message)
}

// 测试工具函数
try {
	console.log('✓ 测试 Telegram 工具函数...')
	
	const { splitTelegramReply, aiMarkdownToTelegramHtml } = await import('./interfaces/telegram/tools.mjs')
	
	// 测试消息分割
	const longMessage = 'A'.repeat(5000)
	const splitMessages = splitTelegramReply(longMessage, 4096)
	console.log(`✓ 消息分割测试: ${longMessage.length} 字符分割为 ${splitMessages.length} 条消息`)
	
	// 测试 Markdown 转换
	const testMarkdown = '**粗体** *斜体* `代码` [链接](https://example.com)'
	const htmlResult = aiMarkdownToTelegramHtml(testMarkdown)
	console.log('✓ Markdown 转换测试:', htmlResult)
	
} catch (error) {
	console.error('✗ 工具函数测试失败:', error.message)
}

console.log('')
console.log('🎉 测试完成！')
console.log('')
console.log('如果所有测试都显示 ✓，说明 Telegram 机器人功能准备就绪。')
console.log('要启动实际的机器人，请：')
console.log('1. 在配置文件中设置真实的 Telegram 机器人令牌')
console.log('2. 运行: deno run --allow-all app.mjs --platform telegram')
console.log('')
console.log('📖 详细设置指南请参考 TELEGRAM-SETUP.md')
