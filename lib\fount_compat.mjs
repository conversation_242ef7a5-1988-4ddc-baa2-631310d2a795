/**
 * fount平台兼容性接口层
 * 提供与fount平台完全兼容的接口，同时保持GentianAphrodite的内部实现不变
 */

import { buildPromptStruct, structPromptToSingleNoChatLog, margeStructPromptChatLog, structPromptToSingle } from './prompt_struct.mjs'
import { GetPrompt, GetPromptForOther } from '../prompt/index.mjs'

/**
 * fount兼容的buildPromptStruct函数
 * 直接导出lib/prompt_struct.mjs中的实现
 */
export { buildPromptStruct }

/**
 * fount兼容的prompt转换函数
 */
export { structPromptToSingleNoChatLog, margeStructPromptChatLog, structPromptToSingle }

/**
 * 为角色接口提供fount兼容的GetPrompt方法
 * 这个函数可以被角色的interfaces.chat.GetPrompt使用
 * @param {Object} args - fount标准参数
 * @param {Object} prompt_struct - 当前构建的prompt_struct
 * @param {number} detail_level - 详细级别
 * @returns {Promise<Object>} 符合fount标准的single_part_prompt_t
 */
export async function fountCompatGetPrompt(args, prompt_struct, detail_level) {
	// 调用GentianAphrodite的GetPrompt，但返回fount期望的格式
	const gentianPrompt = await GetPrompt(args, prompt_struct, detail_level)
	
	// 转换为fount期望的single_part_prompt_t格式
	return {
		text: gentianPrompt.text || [],
		additional_chat_log: gentianPrompt.additional_chat_log || [],
		extension: gentianPrompt.extension || {}
	}
}

/**
 * 为角色接口提供fount兼容的GetPromptForOther方法
 * @param {Object} args - fount标准参数
 * @param {Object} prompt_struct - 当前构建的prompt_struct
 * @param {number} detail_level - 详细级别
 * @returns {Promise<Object>} 符合fount标准的other_chars_prompt_t
 */
export async function fountCompatGetPromptForOther(args, prompt_struct, detail_level) {
	// 调用GentianAphrodite的GetPromptForOther
	const gentianPrompt = await GetPromptForOther(args, prompt_struct, detail_level)
	
	// 转换为fount期望的格式
	return {
		text: gentianPrompt.text || [],
		additional_chat_log: gentianPrompt.additional_chat_log || [],
		extension: gentianPrompt.extension || {}
	}
}

/**
 * 创建一个fount兼容的角色接口包装器
 * 这个函数可以包装现有的GentianAphrodite角色，使其兼容fount平台
 * @param {Object} gentianChar - GentianAphrodite角色对象
 * @returns {Object} fount兼容的角色对象
 */
export function createFountCompatChar(gentianChar) {
	return {
		...gentianChar,
		interfaces: {
			...gentianChar.interfaces,
			chat: {
				...gentianChar.interfaces?.chat,
				GetPrompt: async (args, prompt_struct, detail_level) => {
					// 如果原角色有GetPrompt方法，优先使用
					if (gentianChar.interfaces?.chat?.GetPrompt) {
						return await gentianChar.interfaces.chat.GetPrompt(args, prompt_struct, detail_level)
					}
					// 否则使用兼容包装器
					return await fountCompatGetPrompt(args, prompt_struct, detail_level)
				},
				GetPromptForOther: async (args, prompt_struct, detail_level) => {
					// 如果原角色有GetPromptForOther方法，优先使用
					if (gentianChar.interfaces?.chat?.GetPromptForOther) {
						return await gentianChar.interfaces.chat.GetPromptForOther(args, prompt_struct, detail_level)
					}
					// 否则使用兼容包装器
					return await fountCompatGetPromptForOther(args, prompt_struct, detail_level)
				}
			}
		}
	}
}

/**
 * 验证prompt_struct是否符合fount标准
 * @param {Object} prompt_struct - 要验证的prompt_struct
 * @returns {boolean} 是否符合标准
 */
export function validateFountPromptStruct(prompt_struct) {
	if (!prompt_struct || typeof prompt_struct !== 'object') {
		return false
	}
	
	const requiredFields = ['char_id', 'UserCharname', 'Charname', 'char_prompt', 'user_prompt', 'world_prompt', 'other_chars_prompt', 'plugin_prompts', 'chat_log']
	
	for (const field of requiredFields) {
		if (!(field in prompt_struct)) {
			console.warn(`Missing required field in prompt_struct: ${field}`)
			return false
		}
	}
	
	return true
}

/**
 * 测试fount兼容性的辅助函数
 * @param {Object} testArgs - 测试参数
 * @returns {Promise<boolean>} 测试是否通过
 */
export async function testFountCompatibility(testArgs = {}) {
	try {
		const defaultArgs = {
			char_id: 'test',
			UserCharname: '测试用户',
			Charname: '测试角色',
			chat_log: [],
			...testArgs
		}
		
		// 测试buildPromptStruct
		const prompt_struct = await buildPromptStruct(defaultArgs, 3)
		
		// 验证结构
		if (!validateFountPromptStruct(prompt_struct)) {
			console.error('prompt_struct validation failed')
			return false
		}
		
		// 测试转换函数
		const singlePrompt = structPromptToSingleNoChatLog(prompt_struct)
		const chatLog = margeStructPromptChatLog(prompt_struct)
		const fullPrompt = structPromptToSingle(prompt_struct)
		
		if (typeof singlePrompt !== 'string' || !Array.isArray(chatLog) || typeof fullPrompt !== 'string') {
			console.error('Conversion functions failed')
			return false
		}
		
		console.log('fount compatibility test passed')
		return true
	} catch (error) {
		console.error('fount compatibility test failed:', error)
		return false
	}
}
