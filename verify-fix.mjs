#!/usr/bin/env deno run --allow-all
/**
 * 验证模块引用修复
 */

console.log('🔍 验证模块引用修复...')

const tests = [
	{
		name: '定时器库模块',
		path: './lib/timers.mjs',
		exports: ['getTimers', 'setTimer', 'removeTimer', 'initTimers']
	},
	{
		name: '定时器功能模块',
		path: './reply_gener/functions/timer.mjs',
		exports: ['timer', 'timerCallBack']
	},
	{
		name: '回复生成器主模块',
		path: './reply_gener/index.mjs',
		exports: ['GetReply']
	}
]

let passedTests = 0
let totalTests = tests.length

for (const test of tests) {
	try {
		console.log(`📦 测试 ${test.name}...`)
		const module = await import(test.path)
		
		// 检查导出的函数是否存在
		for (const exportName of test.exports) {
			if (typeof module[exportName] === 'undefined') {
				throw new Error(`缺少导出: ${exportName}`)
			}
		}
		
		console.log(`✅ ${test.name} 导入成功`)
		passedTests++
	} catch (error) {
		console.error(`❌ ${test.name} 导入失败:`, error.message)
	}
}

console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`)

if (passedTests === totalTests) {
	console.log('🎉 所有模块引用修复成功！')
} else {
	console.log('⚠️  仍有模块引用问题需要修复')
	Deno.exit(1)
}
