#!/usr/bin/env deno run --allow-all
/**
 * GentianAphrodite 测试应用程序
 * 简化版本，用于测试基本功能
 */

console.log('GentianAphrodite 测试应用程序启动中...')

// 测试基本的 Deno 功能
try {
	console.log('✓ Deno 运行时正常')
	console.log(`✓ Deno 版本: ${Deno.version.deno}`)
	console.log(`✓ V8 版本: ${Deno.version.v8}`)
	console.log(`✓ TypeScript 版本: ${Deno.version.typescript}`)
} catch (error) {
	console.error('✗ Deno 运行时错误:', error.message)
}

// 测试文件系统访问
try {
	const testDir = './test-data'
	if (!Deno.statSync(testDir).isDirectory) {
		Deno.mkdirSync(testDir, { recursive: true })
	}
	console.log('✓ 文件系统访问正常')
} catch (error) {
	try {
		Deno.mkdirSync('./test-data', { recursive: true })
		console.log('✓ 文件系统访问正常（创建测试目录）')
	} catch (createError) {
		console.error('✗ 文件系统访问错误:', createError.message)
	}
}

// 测试 JSON 处理
try {
	const testConfig = {
		name: 'GentianAphrodite',
		version: '1.0.0',
		test: true
	}
	const jsonString = JSON.stringify(testConfig, null, 2)
	const parsed = JSON.parse(jsonString)
	console.log('✓ JSON 处理正常')
} catch (error) {
	console.error('✗ JSON 处理错误:', error.message)
}

// 测试命令行参数
try {
	console.log(`✓ 命令行参数: ${Deno.args.length} 个参数`)
	if (Deno.args.length > 0) {
		console.log(`  参数: ${Deno.args.join(', ')}`)
	}
} catch (error) {
	console.error('✗ 命令行参数错误:', error.message)
}

// 测试异步功能
try {
	await new Promise(resolve => setTimeout(resolve, 100))
	console.log('✓ 异步功能正常')
} catch (error) {
	console.error('✗ 异步功能错误:', error.message)
}

console.log('\n测试完成！')
console.log('如果所有测试都显示 ✓，说明基本环境正常。')
console.log('现在可以尝试运行完整的应用程序。')
