# 🚀 快速 AI 源配置指南

## 📋 当前状态

✅ Telegram 机器人已成功启动  
❌ **需要配置 AI 源** - 当前显示 "No AI source available"  
✅ 示例配置文件已创建在 `config/aisources/` 目录

## 🔧 快速配置步骤

### 方法 1：使用现有的 OpenAI 兼容 API（推荐）

你的 `config.json` 中已经有一个 OpenAI 配置：

```json
"ai": {
  "sources": {
    "openai": {
      "enabled": false,
      "apiKey": "sk-TzwldRypA2ZEza7OPwFcPe5jduzIarlg8F73VBkUQmBmucdS",
      "model": "gemini-2.5-flash-preview-05-20",
      "endpoint": "https://tbai.xin/v1"
    }
  }
}
```

#### 步骤 1：启用现有配置

编辑 `config/aisources/sfw.json`：

```json
{
  "name": "日常对话模型",
  "type": "openai",
  "endpoint": "https://tbai.xin/v1",
  "apiKey": "sk-TzwldRypA2ZEza7OPwFcPe5jduzIarlg8F73VBkUQmBmucdS",
  "model": "gemini-2.5-flash-preview-05-20",
  "parameters": {
    "temperature": 0.7,
    "top_p": 0.9,
    "max_tokens": 2000
  },
  "maxTokens": 2000,
  "enabled": true,
  "description": "用于日常对话的标准模型"
}
```

**关键变化**：
- `enabled`: `false` → `true`
- `endpoint`: 使用你配置的端点
- `apiKey`: 使用你的 API 密钥
- `model`: 使用你配置的模型

### 方法 2：配置本地模型（免费）

如果你有本地 AI 服务（如 Ollama），编辑 `config/aisources/local.json`：

```json
{
  "name": "本地模型",
  "type": "local",
  "endpoint": "http://localhost:11434",
  "apiKey": "",
  "model": "llama2:7b",
  "parameters": {
    "temperature": 0.7,
    "top_p": 0.9,
    "repeat_penalty": 1.1
  },
  "maxTokens": 2048,
  "enabled": true,
  "description": "本地部署的开源模型"
}
```

### 方法 3：配置多个 AI 源（推荐）

为了获得最佳体验，配置多个 AI 源作为备份：

#### 创建 `config/aisources/detail-thinking.json`：
```json
{
  "name": "详细思考模型",
  "type": "openai",
  "endpoint": "https://tbai.xin/v1",
  "apiKey": "sk-TzwldRypA2ZEza7OPwFcPe5jduzIarlg8F73VBkUQmBmucdS",
  "model": "gemini-2.5-flash-preview-05-20",
  "parameters": {
    "temperature": 0.8,
    "top_p": 0.9,
    "max_tokens": 4000
  },
  "maxTokens": 4000,
  "enabled": true,
  "description": "用于复杂推理和创意任务的高级模型"
}
```

#### 创建 `config/aisources/logic.json`：
```json
{
  "name": "快速逻辑模型",
  "type": "openai",
  "endpoint": "https://tbai.xin/v1",
  "apiKey": "sk-TzwldRypA2ZEza7OPwFcPe5jduzIarlg8F73VBkUQmBmucdS",
  "model": "gemini-2.5-flash-preview-05-20",
  "parameters": {
    "temperature": 0.1,
    "top_p": 0.5,
    "max_tokens": 1000
  },
  "maxTokens": 1000,
  "enabled": true,
  "description": "快速响应的逻辑判断模型"
}
```

## 🧪 测试配置

### 1. 测试 AI 源连接
```bash
deno run --allow-all test-ai-sources.mjs
```

### 2. 启动机器人
```bash
deno run --allow-all app.mjs --platform telegram
```

### 3. 在 Telegram 中测试
发送消息给机器人，看是否能正常回复。

## 🎯 AI 源智能调用顺序

系统会根据任务类型自动选择最合适的 AI 源：

- **日常对话**: `sfw` → `expert` → `detail-thinking` → `logic`
- **复杂思考**: `detail-thinking` → `expert` → `sfw` → `logic`
- **快速判断**: `logic` → `sfw` → `expert` → `detail-thinking`

## 🔧 配置文件位置

```
GentianAphrodite/
├── config.json                    # 主配置文件
└── config/aisources/              # AI 源配置目录
    ├── sfw.json                   # 日常对话模型
    ├── expert.json                # 专家模型
    ├── detail-thinking.json       # 详细思考模型
    ├── logic.json                 # 逻辑模型
    ├── local.json                 # 本地模型
    └── web-browse.json            # 网页浏览模型
```

## 💡 快速解决方案

### 最简单的配置（1分钟完成）

1. **编辑 `config/aisources/sfw.json`**：
   - 将 `enabled` 改为 `true`
   - 使用你现有的 API 配置

2. **重启机器人**：
   ```bash
   deno run --allow-all app.mjs --platform telegram
   ```

3. **测试**：在 Telegram 中发送消息

### 如果你没有 API 密钥

1. **获取免费 API**：
   - [OpenAI](https://platform.openai.com/) - 新用户有免费额度
   - [Anthropic](https://console.anthropic.com/) - Claude API
   - [Google AI](https://ai.google.dev/) - Gemini API

2. **使用本地模型**：
   - 安装 [Ollama](https://ollama.ai/)
   - 运行 `ollama pull llama2`
   - 启用 `local.json` 配置

## 🚨 常见问题

### 问题：显示 "No AI source available"
**解决**：确保至少有一个配置文件的 `enabled` 设为 `true`

### 问题：API 调用失败
**解决**：
1. 检查 API 密钥是否正确
2. 检查网络连接
3. 检查 API 配额

### 问题：机器人不回复
**解决**：
1. 检查 Telegram 机器人令牌
2. 确保 AI 源配置正确
3. 查看控制台错误信息

## 📞 获取帮助

- 查看详细文档：`AI-SOURCES-SETUP.md`
- 运行测试脚本：`deno run --allow-all test-ai-sources.mjs`
- 启用调试模式：`deno run --allow-all app.mjs --platform telegram --debug`

现在你可以开始配置 AI 源了！🚀
