/**
 * 国际化 (i18n) 系统
 * 本地实现，替代 fount 平台的 i18n 系统
 */

import fs from 'node:fs'
import path from 'node:path'
import { loadJsonFileIfExists } from './json_loader.mjs'

/**
 * 支持的语言列表
 */
export const localhostLocales = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR']

/**
 * 默认语言
 */
const DEFAULT_LOCALE = 'zh-CN'

/**
 * 当前语言设置
 */
let currentLocale = DEFAULT_LOCALE

/**
 * 翻译数据缓存
 */
const translationCache = new Map()

/**
 * 语言包目录
 */
const I18N_DIR = './i18n'

/**
 * 初始化翻译系统
 * @param {string} namespace - 命名空间
 * @param {string} locale - 语言代码
 */
export function initTranslations(namespace = 'default', locale = DEFAULT_LOCALE) {
	currentLocale = locale
	loadTranslations(namespace, locale)
	
	// 确保 i18n 目录存在
	if (!fs.existsSync(I18N_DIR)) {
		fs.mkdirSync(I18N_DIR, { recursive: true })
	}
	
	console.log(`Translations initialized for namespace: ${namespace}, locale: ${locale}`)
}

/**
 * 加载翻译数据
 * @param {string} namespace - 命名空间
 * @param {string} locale - 语言代码
 * @returns {Object} 翻译数据
 */
export function loadTranslations(namespace, locale) {
	const cacheKey = `${namespace}:${locale}`
	
	if (translationCache.has(cacheKey)) {
		return translationCache.get(cacheKey)
	}
	
	try {
		const translationPath = path.join(I18N_DIR, namespace, `${locale}.json`)
		const translations = loadJsonFileIfExists(translationPath, {})
		translationCache.set(cacheKey, translations)
		return translations
	} catch (error) {
		console.warn(`Failed to load translations for ${namespace}:${locale}:`, error.message)
		return {}
	}
}

/**
 * 获取翻译文本
 * @param {string} key - 翻译键
 * @param {Object} params - 参数对象
 * @param {string} namespace - 命名空间
 * @param {string} locale - 语言代码
 * @returns {string} 翻译后的文本
 */
export function geti18n(key, params = {}, namespace = 'default', locale = currentLocale) {
	const translations = loadTranslations(namespace, locale)
	
	// 支持嵌套键，如 'user.profile.name'
	const keys = key.split('.')
	let value = translations
	
	for (const k of keys) {
		if (value && typeof value === 'object' && k in value) {
			value = value[k]
		} else {
			// 如果找不到翻译，尝试使用默认语言
			if (locale !== DEFAULT_LOCALE) {
				return geti18n(key, params, namespace, DEFAULT_LOCALE)
			}
			// 如果默认语言也找不到，返回键本身
			return key
		}
	}
	
	// 如果值不是字符串，返回键
	if (typeof value !== 'string') {
		return key
	}
	
	// 替换参数
	return value.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
		return params[paramKey] !== undefined ? String(params[paramKey]) : match
	})
}

/**
 * 设置当前语言
 * @param {string} locale - 语言代码
 */
export function setCurrentLocale(locale) {
	if (localhostLocales.includes(locale)) {
		currentLocale = locale
	} else {
		console.warn(`Unsupported locale: ${locale}, using default: ${DEFAULT_LOCALE}`)
		currentLocale = DEFAULT_LOCALE
	}
}

/**
 * 获取当前语言
 * @returns {string} 当前语言代码
 */
export function getCurrentLocale() {
	return currentLocale
}

/**
 * 检查翻译键是否存在
 * @param {string} key - 翻译键
 * @param {string} namespace - 命名空间
 * @param {string} locale - 语言代码
 * @returns {boolean} 是否存在
 */
export function hasTranslation(key, namespace = 'default', locale = currentLocale) {
	const translations = loadTranslations(namespace, locale)
	const keys = key.split('.')
	let value = translations
	
	for (const k of keys) {
		if (value && typeof value === 'object' && k in value) {
			value = value[k]
		} else {
			return false
		}
	}
	
	return typeof value === 'string'
}

/**
 * 获取所有翻译键
 * @param {string} namespace - 命名空间
 * @param {string} locale - 语言代码
 * @returns {string[]} 翻译键列表
 */
export function getTranslationKeys(namespace = 'default', locale = currentLocale) {
	const translations = loadTranslations(namespace, locale)
	const keys = []
	
	function extractKeys(obj, prefix = '') {
		for (const [key, value] of Object.entries(obj)) {
			const fullKey = prefix ? `${prefix}.${key}` : key
			if (typeof value === 'string') {
				keys.push(fullKey)
			} else if (typeof value === 'object' && value !== null) {
				extractKeys(value, fullKey)
			}
		}
	}
	
	extractKeys(translations)
	return keys
}

/**
 * 格式化复数形式
 * @param {number} count - 数量
 * @param {string} key - 翻译键
 * @param {Object} params - 参数对象
 * @param {string} namespace - 命名空间
 * @param {string} locale - 语言代码
 * @returns {string} 格式化后的文本
 */
export function formatPlural(count, key, params = {}, namespace = 'default', locale = currentLocale) {
	const pluralKey = count === 1 ? `${key}.singular` : `${key}.plural`
	const text = geti18n(pluralKey, { ...params, count }, namespace, locale)
	
	// 如果找不到复数形式，尝试使用基础键
	if (text === pluralKey) {
		return geti18n(key, { ...params, count }, namespace, locale)
	}
	
	return text
}

/**
 * 格式化日期时间
 * @param {Date|number} date - 日期对象或时间戳
 * @param {string} format - 格式类型 ('short', 'medium', 'long', 'full')
 * @param {string} locale - 语言代码
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(date, format = 'medium', locale = currentLocale) {
	const dateObj = date instanceof Date ? date : new Date(date)
	
	const formatOptions = {
		short: { dateStyle: 'short', timeStyle: 'short' },
		medium: { dateStyle: 'medium', timeStyle: 'medium' },
		long: { dateStyle: 'long', timeStyle: 'long' },
		full: { dateStyle: 'full', timeStyle: 'full' }
	}
	
	const options = formatOptions[format] || formatOptions.medium
	
	try {
		return dateObj.toLocaleString(locale, options)
	} catch (error) {
		return dateObj.toLocaleString(DEFAULT_LOCALE, options)
	}
}

/**
 * 格式化数字
 * @param {number} number - 数字
 * @param {Object} options - 格式化选项
 * @param {string} locale - 语言代码
 * @returns {string} 格式化后的数字
 */
export function formatNumber(number, options = {}, locale = currentLocale) {
	try {
		return number.toLocaleString(locale, options)
	} catch (error) {
		return number.toLocaleString(DEFAULT_LOCALE, options)
	}
}

/**
 * 格式化货币
 * @param {number} amount - 金额
 * @param {string} currency - 货币代码
 * @param {string} locale - 语言代码
 * @returns {string} 格式化后的货币
 */
export function formatCurrency(amount, currency = 'CNY', locale = currentLocale) {
	const options = {
		style: 'currency',
		currency: currency
	}
	
	try {
		return amount.toLocaleString(locale, options)
	} catch (error) {
		return amount.toLocaleString(DEFAULT_LOCALE, options)
	}
}

/**
 * 清除翻译缓存
 * @param {string} namespace - 命名空间（可选）
 */
export function clearTranslationCache(namespace = null) {
	if (namespace) {
		for (const key of translationCache.keys()) {
			if (key.startsWith(`${namespace}:`)) {
				translationCache.delete(key)
			}
		}
	} else {
		translationCache.clear()
	}
}

/**
 * 重新加载翻译
 * @param {string} namespace - 命名空间
 * @param {string} locale - 语言代码
 */
export function reloadTranslations(namespace = 'default', locale = currentLocale) {
	clearTranslationCache(namespace)
	loadTranslations(namespace, locale)
}

// 默认初始化
initTranslations()
