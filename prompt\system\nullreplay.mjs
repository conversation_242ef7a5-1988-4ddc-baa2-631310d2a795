/** @typedef {import("../../types/chatLog.mjs").chatLogEntry_t} chatLogEntry_t */
/** @typedef {import("../logical_results/index.mjs").logical_results_t} logical_results_t */

/**
 * @typedef {Object} chatReplyRequest_t
 * @property {chatLogEntry_t[]} chat_log - 聊天日志
 * @property {string} chat_name - 聊天名称
 * @property {string} UserCharname - 用户角色名
 * @property {string} Charname - 角色名
 * @property {string[]} locales - 语言设置
 */

/**
 * @typedef {Object} prompt_struct_t
 * @property {Object[]} messages - 消息列表
 * @property {string} systemPrompt - 系统提示词
 * @property {string} userPrompt - 用户提示词
 * @property {Object[]} context - 上下文
 */

/**
 * @param {chatReplyRequest_t} args
 * @param {logical_results_t} logical_results
 * @param {prompt_struct_t} prompt_struct
 * @param {number} detail_level
 */
export async function NullReplayPrompt(args, logical_results, prompt_struct, detail_level) {
	let result = ''

	if (logical_results.in_muti_char_chat || args.extension?.from_timer)
		result += `\
你若认为当前语境不适合或无需回复，可以单纯输出<-<null>->来跳过回复。
如：
${args.UserCharname}: 那张三你觉得我什么时候去合适？
龙胆: <-<null>->
`

	return {
		text: [{
			content: result,
			important: 0
		}],
		additional_chat_log: []
	}
}
