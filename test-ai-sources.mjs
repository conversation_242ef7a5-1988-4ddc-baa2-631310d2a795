#!/usr/bin/env deno run --allow-all
/**
 * AI 源测试脚本
 * 测试所有配置的 AI 源是否正常工作
 */

import { getAISourcesManager } from './lib/AIsources_manager.mjs'
import { AIsources, GetAISourceCallingOrder } from './AISource/index.mjs'
import { loadJsonFileIfExists } from './lib/json_loader.mjs'
import fs from 'node:fs'
import path from 'node:path'

console.log('🤖 AI 源测试工具')
console.log('=' .repeat(50))

/**
 * 测试单个 AI 源
 * @param {string} name - AI 源名称
 * @param {string} filename - 配置文件名
 */
async function testAISource(name, filename) {
	console.log(`\n📦 测试 ${name} (${filename})...`)
	
	try {
		const configPath = `./config/aisources/${filename}`
		
		// 检查配置文件是否存在
		if (!fs.existsSync(configPath)) {
			console.log(`❌ 配置文件不存在: ${configPath}`)
			return false
		}
		
		// 加载配置
		const config = loadJsonFileIfExists(configPath)
		if (!config) {
			console.log(`❌ 配置文件格式错误`)
			return false
		}
		
		console.log(`📋 配置信息:`)
		console.log(`   名称: ${config.name || filename}`)
		console.log(`   类型: ${config.type || 'unknown'}`)
		console.log(`   模型: ${config.model || 'unknown'}`)
		console.log(`   端点: ${config.endpoint || 'unknown'}`)
		console.log(`   启用: ${config.enabled ? '是' : '否'}`)
		
		if (!config.enabled) {
			console.log(`⚠️  AI 源已禁用`)
			return false
		}
		
		// 加载 AI 源
		const manager = getAISourcesManager()
		const source = await manager.loadAIsource(filename)
		
		if (!source) {
			console.log(`❌ AI 源加载失败`)
			return false
		}
		
		console.log(`✅ AI 源加载成功`)
		
		// 测试连接
		console.log(`🔗 测试连接...`)
		const testResult = await source.test()
		
		if (testResult) {
			console.log(`✅ 连接测试成功`)
			
			// 获取状态信息
			const info = source.getInfo()
			console.log(`📊 状态信息:`)
			console.log(`   可用: ${info.status.available ? '是' : '否'}`)
			console.log(`   请求次数: ${info.status.requestCount}`)
			console.log(`   错误次数: ${info.status.errorCount}`)
			
			return true
		} else {
			console.log(`❌ 连接测试失败`)
			return false
		}
		
	} catch (error) {
		console.log(`❌ 测试失败: ${error.message}`)
		return false
	}
}

/**
 * 测试 AI 源调用顺序
 */
function testCallingOrder() {
	console.log(`\n🔄 AI 源调用顺序测试`)
	console.log('-'.repeat(30))
	
	const scenarios = [
		'detail-thinking',
		'expert', 
		'sfw',
		'web-browse',
		'nsfw',
		'logic'
	]
	
	for (const scenario of scenarios) {
		const order = GetAISourceCallingOrder(scenario)
		console.log(`${scenario.padEnd(15)}: ${order.join(' → ')}`)
	}
}

/**
 * 检查配置目录
 */
function checkConfigDirectory() {
	console.log(`\n📁 检查配置目录...`)
	
	const configDir = './config/aisources'
	
	if (!fs.existsSync(configDir)) {
		console.log(`❌ 配置目录不存在: ${configDir}`)
		console.log(`💡 创建配置目录...`)
		try {
			fs.mkdirSync(configDir, { recursive: true })
			console.log(`✅ 配置目录已创建`)
		} catch (error) {
			console.log(`❌ 创建配置目录失败: ${error.message}`)
			return false
		}
	} else {
		console.log(`✅ 配置目录存在`)
	}
	
	// 列出配置文件
	try {
		const files = fs.readdirSync(configDir).filter(f => f.endsWith('.json'))
		console.log(`📋 发现 ${files.length} 个配置文件:`)
		for (const file of files) {
			console.log(`   - ${file}`)
		}
		return files
	} catch (error) {
		console.log(`❌ 读取配置目录失败: ${error.message}`)
		return []
	}
}

/**
 * 创建示例配置文件
 */
async function createExampleConfigs() {
	console.log(`\n📝 创建示例配置文件...`)
	
	const configDir = './config/aisources'
	
	// 示例配置
	const examples = {
		'sfw.json': {
			name: "日常对话模型",
			type: "openai",
			endpoint: "https://api.openai.com/v1",
			apiKey: "sk-your-openai-api-key-here",
			model: "gpt-3.5-turbo",
			parameters: {
				temperature: 0.7,
				top_p: 0.9,
				max_tokens: 2000
			},
			maxTokens: 2000,
			enabled: false,
			description: "用于日常对话的标准模型"
		},
		'expert.json': {
			name: "专家咨询模型",
			type: "openai", 
			endpoint: "https://api.openai.com/v1",
			apiKey: "sk-your-openai-api-key-here",
			model: "gpt-4",
			parameters: {
				temperature: 0.8,
				top_p: 0.9,
				max_tokens: 4000
			},
			maxTokens: 4000,
			enabled: false,
			description: "用于专业问题和技术咨询的高级模型"
		},
		'local.json': {
			name: "本地模型",
			type: "local",
			endpoint: "http://localhost:11434",
			apiKey: "",
			model: "llama2:7b",
			parameters: {
				temperature: 0.7,
				top_p: 0.9,
				repeat_penalty: 1.1
			},
			maxTokens: 2048,
			enabled: false,
			description: "本地部署的开源模型"
		}
	}
	
	for (const [filename, config] of Object.entries(examples)) {
		const filePath = path.join(configDir, filename)
		
		if (!fs.existsSync(filePath)) {
			try {
				fs.writeFileSync(filePath, JSON.stringify(config, null, 2))
				console.log(`✅ 创建示例配置: ${filename}`)
			} catch (error) {
				console.log(`❌ 创建配置失败 ${filename}: ${error.message}`)
			}
		} else {
			console.log(`⚠️  配置文件已存在: ${filename}`)
		}
	}
}

/**
 * 主函数
 */
async function main() {
	try {
		// 检查配置目录
		const configFiles = checkConfigDirectory()
		
		// 如果没有配置文件，创建示例
		if (configFiles.length === 0) {
			await createExampleConfigs()
			console.log(`\n💡 已创建示例配置文件，请编辑配置文件并设置正确的 API 密钥`)
			console.log(`📁 配置目录: ./config/aisources/`)
			console.log(`📖 参考文档: AI-SOURCES-SETUP.md`)
			return
		}
		
		// 测试调用顺序
		testCallingOrder()
		
		// 测试每个 AI 源
		console.log(`\n🧪 开始测试 AI 源...`)
		
		let totalTests = 0
		let passedTests = 0
		
		// 测试已配置的 AI 源
		const aiSourceTypes = ['detail-thinking', 'expert', 'sfw', 'web-browse', 'nsfw', 'logic']
		
		for (const type of aiSourceTypes) {
			const filename = `${type}.json`
			totalTests++
			
			const result = await testAISource(type, filename)
			if (result) {
				passedTests++
			}
		}
		
		// 测试其他配置文件
		for (const filename of configFiles) {
			if (!aiSourceTypes.some(type => filename === `${type}.json`)) {
				totalTests++
				const name = filename.replace('.json', '')
				const result = await testAISource(name, filename)
				if (result) {
					passedTests++
				}
			}
		}
		
		// 总结
		console.log(`\n${'='.repeat(50)}`)
		console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`)
		
		if (passedTests === totalTests && totalTests > 0) {
			console.log(`🎉 所有 AI 源测试通过！`)
		} else if (passedTests > 0) {
			console.log(`⚠️  部分 AI 源测试失败，请检查配置`)
		} else {
			console.log(`❌ 所有 AI 源测试失败，请检查配置和网络连接`)
		}
		
		console.log(`\n💡 提示:`)
		console.log(`- 编辑配置文件设置正确的 API 密钥`)
		console.log(`- 确保网络连接正常`)
		console.log(`- 检查 API 配额和权限`)
		console.log(`- 参考 AI-SOURCES-SETUP.md 获取详细配置指南`)
		
	} catch (error) {
		console.error(`❌ 测试过程中出错:`, error.message)
		if (globalThis.DEBUG) {
			console.error(error.stack)
		}
		Deno.exit(1)
	}
}

// 运行测试
if (import.meta.main) {
	main()
}
