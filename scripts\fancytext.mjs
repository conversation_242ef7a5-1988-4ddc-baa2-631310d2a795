const map = {
	'&': ['⅋'],
	'%': ['⅍', '℀', '℁', '℆', '℅'],
	'0': ['０', 'Ѳ', 'ʘ'],
	'1': ['➀', '❶', '１'],
	'2': ['２', '❷', '➁'],
	'3': ['３', '❸', '➂'],
	'4': ['４', '❹', '➃'],
	'5': ['❺', '➄', '５'],
	'6': ['６', '❻', '➅'],
	'7': ['７', '❼', '➆'],
	'8': ['８', '➇', '❽'],
	'9': ['➈', '❾', '９'],
	'<': ['≼', '≺', '≪', '☾', '≾', '⋜', '⋞', '⋐', '⊂', '⊏', '⊑', '《', '＜', '❮', '❰', '⫷'],
	'>': ['☽', '≫', '≻', '≽', '≿', '⋝', '⋟', '⋑', '⊃', '⊐', '⊒', '⫸', '》', '＞', '❯❱'],
	'[': ['【', '〖', '〘', '〚', '［'],
	']': ['】', '〗', '〙', '〛', '］'],
	'*': ['✨', '✩', '✪', '✫', '✬', '✭', '✮', '✯', '✰', '✦', '✱', '✲', '✳', '✴', '✵', '✶', '✷', '֍', '֎', '✸', '✹', '✺', '✻', '✼', '✽', '✾', '✿', '❀', '❁', '❂', '❃', '❄', '★', '☆', '＊'],
	'(': ['（'],
	')': ['）'],
	'a': ['ⓐ', 'α', 'ａ', 'Δ', 'ค', 'α', 'ά', '𝔞', '𝓪', '𝒶', '𝐚', '𝕒', 'ᵃ', '𝙖'],
	'b': ['ⓑ', 'ｂ', 'β', '๒', 'β', '𝔟', '𝓫', '𝒷', '𝐛', '𝕓', 'ᵇ', '𝙗'],
	'c': ['ⓒ', '¢', 'ｃ', 'Ć', 'ς', 'c', 'ς', '𝔠', '𝓬', '𝒸', '𝐜', '𝕔', 'ᶜ', '𝙘'],
	'd': ['ⓓ', '∂', 'ｄ', '๔', '∂', 'đ', '𝔡', '𝓭', '𝒹', '𝐝', '𝕕', 'ᵈ', '𝙙'],
	'e': ['ⓔ', 'є', 'ｅ', '€', 'є', 'ε', 'έ', '𝔢', '𝒆', '𝑒', '𝓔', '𝐞', '𝕖', 'ᵉ', '𝙚'],
	'f': ['ⓕ', 'ƒ', 'ｆ', 'ℱ', 'Ŧ', 'ғ', 'ғ', '𝔣', '𝒇', '𝒻', '𝐟', '𝕗', 'ᶠ', '𝙛'],
	'g': ['ⓖ', 'ق', 'g', 'ｇ', 'ﻮ', 'g', 'ģ', '𝔤', '𝓰', '𝑔', '𝓖', '𝐠', '𝕘', 'ᵍ', '𝙜'],
	'h': ['ⓗ', 'ｈ', 'ђ', 'ħ', '𝔥', '𝓱', '𝒽', '𝐡', '𝕙', 'ʰ', '𝙝'],
	'i': ['ⓘ', 'ι', 'ｉ', 'Ꭵ', 'เ', 'ι', 'ί', '𝔦', '𝓲', '𝒾', '𝐢', '𝕚', 'ᶤ', '𝙞'],
	'j': ['ⓙ', 'נ', 'ڶ', 'ｊ', 'ᒎ', 'Ĵ', 'ן', 'נ', 'ј', 'Ĵ', '𝔧', '𝓳', '𝒿', '𝐣', '𝕛', 'ʲ'],
	'k': ['ⓚ', 'к', 'ｋ', 'к', 'к', 'ķ', 'Ќ', '𝔨', '𝓴', '𝓀', '𝐤', '𝕂', '𝕜', 'ᵏ', '𝙠'],
	'l': ['ⓛ', 'ℓ', 'ｌ', 'Ł', 'l', 'ℓ', '𝔩', '𝓵', '𝓁', '𝓛', '𝐥', '𝕝', 'ˡ', '𝙡'],
	'm': ['ⓜ', 'м', 'ｍ', '๓', 'м', 'м', 'ϻ', '𝔪', '𝓶', '𝓂', '𝐦', '𝕞', 'ᵐ', '𝙢'],
	'n': ['ⓝ', 'η', 'ｎ', 'ภ', 'η', 'ή', '𝔫', '𝓷', '𝓃', '𝐧', '𝕟', 'ᶰ', '𝙣'],
	'o': ['ㄖ', 'σ', 'ｏ', 'Ø', '๏', 'σ', 'ό', 'Ỗ', '𝔬', '𝓸', '𝑜', '𝐨', '𝕠', 'ᵒ', '𝙤'],
	'p': ['ⓟ', 'ρ', 'ｐ', 'ᑭ', 'Ƥ', 'ק', 'ρ', 'ρ', 'Ƥ', '𝔭', '𝓹', '𝓅', '𝐩', '𝕡', 'ᵖ', '𝙥'],
	'q': ['ⓠ', 'q', 'ｑ', 'Ɋ', 'ợ', 'q', 'q', '𝔮', '𝓺', '𝓆', '𝐪', '𝕢', 'ᵠ'],
	'r': ['ⓡ', 'ｒ', 'г', 'ŕ', '𝔯', '𝓻', '𝓇', '𝐫', '𝕣', 'ʳ', '𝙧'],
	's': ['ⓢ', 'ѕ', 'ｓ', 'ร', 's', 'ş', '𝔰', '𝓼', '𝓈', '𝐬', '𝕤', 'ˢ', '𝙨'],
	't': ['ⓣ', 'т', 'ｔ', 'Ŧ', 't', 'т', 'ţ', '𝔱', '𝓽', '𝓉', '𝐭', '𝕥', 'ᵗ', '𝙩'],
	'u': ['ⓤ', 'υ', 'ｕ', 'Ữ', 'ย', 'υ', 'ù', '𝔲', '𝓾', '𝓊', '𝐮', '𝕦', 'ᵘ', '𝙪'],
	'v': ['ⓥ', 'ν', 'ｖ', 'ש', 'v', 'ν', '𝔳', '𝓿', '𝓋', '𝐯', '𝕧', 'ᵛ', '𝙫'],
	'w': ['ⓦ', 'ω', 'ｗ', 'ฬ', 'ω', 'ώ', '𝔴', '𝔀', '𝓌', '𝐰', '𝕨', 'ʷ', '𝙬'],
	'x': ['ⓧ', 'ｘ', 'Ж', 'א', 'x', 'x', 'Ж', '𝔵', '𝔁', '𝓍', '𝐱', '𝕩', 'ˣ'],
	'y': ['ⓨ', 'у', 'ｙ', '¥', 'ץ', 'ү', 'ч', 'Ў', '𝔶', '𝔂', '𝓎', '𝐲', '𝕪', 'ʸ', '𝙮'],
	'z': ['ⓩ', 'z', 'ｚ', 'Ƶ', 'z', 'z', 'ž', 'Ż', '𝔷', '𝔃', '𝓏', '𝐳', '𝕫', 'ᶻ'],
	'A': ['Ⓐ', 'Ａ', 'ᗩ', '卂', 'Δ', 'Ã', '𝓐', '𝐀', '𝔸', '𝘼'],
	'B': ['Ⓑ', 'в', 'Ｂ', '乃', 'ᗷ', 'в', 'в', '𝓑', '𝐁', '𝔹', '𝘽'],
	'C': ['Ⓒ', '匚', 'Ｃ', 'ᑕ', 'Č', '℃', '𝓒', '𝐂', 'ℂ', '𝘾'],
	'D': ['Ⓓ', 'Ｄ', 'ᗪ', 'Đ', 'Ď', '𝓓', '𝐃', 'ᗪ', '𝔻', '𝘿'],
	'E': ['Ⓔ', '乇', 'Ｅ', 'ᗴ', 'Ẹ', '𝐄', '𝔼'],
	'F': ['Ⓕ', 'Ｆ', '千', 'ᖴ', 'Ƒ', '𝓕', '𝐅', '𝔽', '𝙁'],
	'G': ['Ⓖ', 'Ｇ', 'Ǥ', 'Ꮆ', 'Ğ', '𝐆', '𝔾', 'Ꮆ'],
	'H': ['Ⓗ', '卄', 'н', 'Ｈ', 'ᕼ', 'Ħ', 'н', 'Ĥ', '𝓗', '𝐇', 'ℍ'],
	'I': ['Ⓘ', 'ι', 'Ｉ', '丨', 'Ɨ', 'Į', '𝓘', '𝐈', '𝕀'],
	'J': ['Ⓙ', 'Ｊ', '𝓙', '𝐉', '𝕁'],
	'K': ['Ⓚ', 'Ｋ', 'ᛕ', '𝓚', '𝐊', 'Ҝ'],
	'L': ['Ⓛ', 'ㄥ', 'Ｌ', 'ᒪ', 'Ļ', 'Ĺ', '𝐋', '𝕃'],
	'M': ['Ⓜ', 'Ｍ', 'ᗰ', 'Μ', '𝓜', '𝐌', '𝕄', '爪', '𝙈'],
	'N': ['Ⓝ', '几', 'Ｎ', 'ᑎ', 'Ň', 'Ň', '𝓝', '𝐍', 'ℕ', '𝙉'],
	'O': ['Ⓞ', 'ⓞ', 'Ｏ', 'ᗝ', '𝓞', '𝐎', '𝕆', '𝙊'],
	'P': ['Ⓟ', 'Ｐ', '卩', '𝓟', '𝐏', 'ℙ', '𝙋'],
	'Q': ['Ⓠ', 'Ｑ', 'Ω', 'Ǫ', '𝓠', '𝐐', 'ℚ'],
	'R': ['Ⓡ', 'я', '尺', 'Ｒ', 'ᖇ', 'Ř', 'я', 'Ř', '𝓡', '𝐑', 'ℝ'],
	'S': ['Ⓢ', 'Ｓ', '丂', 'ᔕ', 'Ş', 'Ŝ', '𝓢', '𝐒', '𝕊', '𝙎'],
	'T': ['Ⓣ', 'Ｔ', '丅', 'Ť', '𝓣', '𝐓', '𝕋'],
	'U': ['Ⓤ', 'Ｕ', 'ᑌ', 'Ǘ', '𝓤', '𝐔', '𝕌'],
	'V': ['Ⓥ', 'Ｖ', 'ᐯ', 'V', 'Ѷ', '𝓥', '𝐕', '𝕍'],
	'W': ['Ⓦ', 'Ｗ', 'ᗯ', 'Ŵ', '𝓦', '𝐖', '𝕎', '山'],
	'X': ['Ⓧ', 'χ', 'Ｘ', '乂', '᙭', '𝓧', '𝐗', '𝕏'],
	'Y': ['Ⓨ', 'ㄚ', 'Ｙ', 'Ƴ', '𝓨', '𝐘', '𝕐'],
	'Z': ['Ⓩ', '乙', 'Ｚ', 'Ž', '𝓩', '𝐙', 'ℤ']
}

export function normalizeFancyText(text) {
	for (const [char, variants] of Object.entries(map))
		for (const variant of variants)
			text = text.replaceAll(variant, char)
	return text
}
