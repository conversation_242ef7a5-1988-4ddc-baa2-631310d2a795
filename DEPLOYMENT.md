# GentianAphrodite 独立应用部署指南

本文档详细说明如何在各种环境中部署 GentianAphrodite 独立应用程序。

## 系统要求

### 最低要求
- **CPU**: 1 核心
- **内存**: 512MB RAM
- **存储**: 100MB 可用空间
- **网络**: 互联网连接（用于 AI API 调用）

### 推荐配置
- **CPU**: 2+ 核心
- **内存**: 2GB+ RAM
- **存储**: 1GB+ 可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **Deno**: 1.40+ (必需)
- **操作系统**: Windows 10+, macOS 10.15+, Linux (任何现代发行版)

## 安装 Deno

### Windows

#### 方法 1: PowerShell (推荐)
```powershell
irm https://deno.land/install.ps1 | iex
```

#### 方法 2: Chocolatey
```powershell
choco install deno
```

#### 方法 3: Scoop
```powershell
scoop install deno
```

#### 方法 4: 手动安装
1. 从 [Deno Releases](https://github.com/denoland/deno/releases) 下载最新版本
2. 解压到 `C:\deno\`
3. 将 `C:\deno\` 添加到系统 PATH

### macOS

#### 方法 1: Homebrew (推荐)
```bash
brew install deno
```

#### 方法 2: curl
```bash
curl -fsSL https://deno.land/install.sh | sh
```

#### 方法 3: 手动安装
1. 从 [Deno Releases](https://github.com/denoland/deno/releases) 下载 macOS 版本
2. 解压到 `/usr/local/bin/` 或 `~/.local/bin/`
3. 确保目录在 PATH 中

### Linux

#### 方法 1: curl (推荐)
```bash
curl -fsSL https://deno.land/install.sh | sh
```

#### 方法 2: 包管理器

**Ubuntu/Debian:**
```bash
sudo apt install deno
```

**Arch Linux:**
```bash
sudo pacman -S deno
```

**Fedora:**
```bash
sudo dnf install deno
```

**Snap:**
```bash
sudo snap install deno
```

## 应用程序部署

### 1. 获取源代码

#### 从 GitHub 克隆
```bash
git clone https://github.com/steve02081504/GentianAphrodite.git
cd GentianAphrodite
```

#### 下载 ZIP 包
1. 访问 [GitHub 仓库](https://github.com/steve02081504/GentianAphrodite)
2. 点击 "Code" -> "Download ZIP"
3. 解压到目标目录

### 2. 配置应用程序

#### 创建配置文件
```bash
cp config.example.json config.json
```

#### 编辑配置文件
使用文本编辑器编辑 `config.json`：

```json
{
  "username": "YourUsername",
  "platform": "shell",
  "locale": "zh-CN",
  "debug": false,
  "ai": {
    "defaultSource": "openai",
    "maxTokens": 4000,
    "temperature": 0.7,
    "sources": {
      "openai": {
        "enabled": true,
        "apiKey": "YOUR_OPENAI_API_KEY",
        "model": "gpt-3.5-turbo"
      }
    }
  }
}
```

### 3. 运行应用程序

#### 使用启动脚本 (推荐)

**Linux/macOS:**
```bash
chmod +x start.sh
./start.sh
```

**Windows:**
```cmd
start.bat
```

#### 直接运行
```bash
deno run --allow-all app.mjs
```

#### 带参数运行
```bash
deno run --allow-all app.mjs --username "Alice" --platform "shell" --debug
```

## 平台特定部署

### Discord 机器人部署

1. **创建 Discord 应用程序**
   - 访问 [Discord Developer Portal](https://discord.com/developers/applications)
   - 创建新应用程序
   - 在 "Bot" 页面创建机器人
   - 复制机器人令牌

2. **配置 Discord 设置**
   ```json
   {
     "platform": "discord",
     "discord": {
       "token": "YOUR_BOT_TOKEN",
       "clientId": "YOUR_CLIENT_ID",
       "guildId": "YOUR_GUILD_ID"
     }
   }
   ```

3. **启动 Discord 机器人**
   ```bash
   deno run --allow-all app.mjs --platform discord
   ```

### Telegram 机器人部署

1. **创建 Telegram 机器人**
   - 与 [@BotFather](https://t.me/botfather) 对话
   - 使用 `/newbot` 命令创建机器人
   - 获取机器人令牌

2. **配置 Telegram 设置**
   ```json
   {
     "platform": "telegram",
     "telegram": {
       "token": "YOUR_BOT_TOKEN",
       "botUsername": "your_bot_username"
     }
   }
   ```

3. **启动 Telegram 机器人**
   ```bash
   deno run --allow-all app.mjs --platform telegram
   ```

## 生产环境部署

### 使用 systemd (Linux)

1. **创建服务文件**
   ```bash
   sudo nano /etc/systemd/system/gentian-aphrodite.service
   ```

2. **服务配置**
   ```ini
   [Unit]
   Description=GentianAphrodite AI Character
   After=network.target

   [Service]
   Type=simple
   User=gentian
   WorkingDirectory=/opt/gentian-aphrodite
   ExecStart=/usr/local/bin/deno run --allow-all app.mjs
   Restart=always
   RestartSec=10

   [Install]
   WantedBy=multi-user.target
   ```

3. **启用和启动服务**
   ```bash
   sudo systemctl enable gentian-aphrodite
   sudo systemctl start gentian-aphrodite
   ```

### 使用 Docker

1. **创建 Dockerfile**
   ```dockerfile
   FROM denoland/deno:alpine

   WORKDIR /app
   COPY . .

   RUN deno cache app.mjs

   EXPOSE 8000

   CMD ["deno", "run", "--allow-all", "app.mjs"]
   ```

2. **构建镜像**
   ```bash
   docker build -t gentian-aphrodite .
   ```

3. **运行容器**
   ```bash
   docker run -d --name gentian-aphrodite \
     -v $(pwd)/config.json:/app/config.json \
     -v $(pwd)/data:/app/data \
     gentian-aphrodite
   ```

### 使用 PM2 (Node.js 进程管理器)

1. **安装 PM2**
   ```bash
   npm install -g pm2
   ```

2. **创建 PM2 配置**
   ```json
   {
     "name": "gentian-aphrodite",
     "script": "deno",
     "args": ["run", "--allow-all", "app.mjs"],
     "cwd": "/path/to/gentian-aphrodite",
     "instances": 1,
     "autorestart": true,
     "watch": false,
     "max_memory_restart": "1G"
   }
   ```

3. **启动应用**
   ```bash
   pm2 start ecosystem.config.json
   pm2 save
   pm2 startup
   ```

## 监控和维护

### 日志管理
- 日志文件位置: `./logs/`
- 日志级别: info, warn, error, debug
- 日志轮转: 自动管理，最多保留 10 个文件

### 性能监控
```bash
# 查看系统资源使用
top -p $(pgrep -f "deno.*app.mjs")

# 查看内存使用
ps aux | grep "deno.*app.mjs"

# 查看网络连接
netstat -tulpn | grep deno
```

### 备份策略
```bash
# 备份配置和数据
tar -czf backup-$(date +%Y%m%d).tar.gz config.json data/ logs/

# 定期备份脚本
#!/bin/bash
BACKUP_DIR="/backup/gentian-aphrodite"
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "$BACKUP_DIR/backup_$DATE.tar.gz" config.json data/ logs/
find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +7 -delete
```

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   # 确保有正确的文件权限
   chmod +x start.sh
   chmod 644 config.json
   ```

2. **端口占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :8000
   # 或
   netstat -tulpn | grep :8000
   ```

3. **内存不足**
   ```bash
   # 监控内存使用
   free -h
   # 增加交换空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### 调试模式
```bash
# 启用详细日志
deno run --allow-all app.mjs --debug

# 查看实时日志
tail -f logs/app.log
```

## 安全建议

1. **API 密钥安全**
   - 使用环境变量存储敏感信息
   - 定期轮换 API 密钥
   - 限制 API 密钥权限

2. **网络安全**
   - 使用防火墙限制访问
   - 启用 HTTPS (如果提供 Web 服务)
   - 定期更新系统和依赖

3. **访问控制**
   - 限制文件权限
   - 使用专用用户运行应用
   - 启用审计日志

## 更新和升级

### 更新应用程序
```bash
# 备份当前版本
cp -r . ../gentian-aphrodite-backup

# 拉取最新代码
git pull origin main

# 重启应用
./start.sh
```

### 更新 Deno
```bash
# 更新到最新版本
deno upgrade

# 更新到特定版本
deno upgrade --version 1.40.0
```

## 支持和帮助

如果遇到问题，请：

1. 查看日志文件
2. 启用调试模式
3. 检查配置文件
4. 查阅文档
5. 提交 Issue 到 GitHub 仓库

---

**注意**: 本指南假设您有基本的系统管理经验。如果您是初学者，建议先在测试环境中练习部署流程。
