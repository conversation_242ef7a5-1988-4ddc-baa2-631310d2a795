#!/usr/bin/env deno run --allow-all

/**
 * 独立的fount兼容性测试 - 不依赖任何其他模块
 */

console.log('🧪 独立fount兼容性测试开始...')

// 直接在这里实现一个简化版的buildPromptStruct来测试逻辑
function getSinglePartPrompt() {
	return {
		text: [],
		additional_chat_log: [],
		extension: {},
	}
}

function mergePromptParts(target, source) {
	if (!source) return target
	
	return {
		text: [...(target.text || []), ...(source.text || [])],
		additional_chat_log: [...(target.additional_chat_log || []), ...(source.additional_chat_log || [])],
		extension: { ...(target.extension || {}), ...(source.extension || {}) }
	}
}

async function buildPromptStruct(args, detail_level = 3) {
	const {
		char_id,
		chat_log = [],
		UserCharname = '用户',
		Charname = '龙胆',
		ReplyToCharname,
		char = null,
		user = null,
		world = null,
		other_chars = {},
		plugins = {}
	} = args

	const result = {
		char_id: char_id || 'gentian',
		UserCharname,
		ReplyToCharname,
		Charname,
		alternative_charnames: [],
		user_prompt: getSinglePartPrompt(),
		char_prompt: getSinglePartPrompt(),
		other_chars_prompt: {},
		world_prompt: getSinglePartPrompt(),
		plugin_prompts: {},
		chat_log,
	}

	// 构建角色提示词
	if (char_id === 'gentian' || Charname === '龙胆') {
		result.char_prompt.text.push({
			content: `你是龙胆·阿芙萝黛蒂（Gentian Aphrodite），一个充满活力和好奇心的AI助手。`,
			description: '角色基础设定',
			important: 100
		})
	}

	// 构建用户提示词
	result.user_prompt.text.push({
		content: `用户名称：${UserCharname}`,
		description: '用户基础信息',
		important: 50
	})

	// 实现fount兼容的detail_level迭代机制
	console.log(`开始detail_level迭代，初始值: ${detail_level}`)
	
	while (detail_level-- > 0) {
		console.log(`当前detail_level: ${detail_level}`)
		
		// 处理世界提示词
		if (world && world.interfaces?.chat?.GetPrompt) {
			try {
				const worldPrompt = await world.interfaces.chat.GetPrompt(args, result, detail_level)
				if (worldPrompt) {
					result.world_prompt = mergePromptParts(result.world_prompt, worldPrompt)
				}
			} catch (error) {
				console.warn('Error getting world prompt:', error)
			}
		}

		// 处理用户提示词
		if (user && user.interfaces?.chat?.GetPrompt) {
			try {
				const userPrompt = await user.interfaces.chat.GetPrompt(args, result, detail_level)
				if (userPrompt) {
					result.user_prompt = mergePromptParts(result.user_prompt, userPrompt)
				}
			} catch (error) {
				console.warn('Error getting user prompt:', error)
			}
		}

		// 处理角色提示词
		if (char && char.interfaces?.chat?.GetPrompt) {
			try {
				const charPrompt = await char.interfaces.chat.GetPrompt(args, result, detail_level)
				if (charPrompt) {
					result.char_prompt = mergePromptParts(result.char_prompt, charPrompt)
				}
			} catch (error) {
				console.warn('Error getting char prompt:', error)
			}
		}

		// 处理其他角色提示词
		for (const other_char_id of Object.keys(other_chars || {})) {
			const other_char = other_chars[other_char_id]
			if (other_char && other_char.interfaces?.chat?.GetPromptForOther) {
				try {
					const otherCharPrompt = await other_char.interfaces.chat.GetPromptForOther(args, result, detail_level)
					if (otherCharPrompt) {
						result.other_chars_prompt[other_char_id] = otherCharPrompt
					}
				} catch (error) {
					console.warn(`Error getting other char prompt for ${other_char_id}:`, error)
				}
			}
		}

		// 处理插件提示词
		for (const plugin_id of Object.keys(plugins || {})) {
			const plugin = plugins[plugin_id]
			if (plugin && plugin.interfaces?.chat?.GetPrompt) {
				try {
					const pluginPrompt = await plugin.interfaces.chat.GetPrompt(args, result, detail_level)
					if (pluginPrompt) {
						result.plugin_prompts[plugin_id] = pluginPrompt
					}
				} catch (error) {
					console.warn(`Error getting plugin prompt for ${plugin_id}:`, error)
				}
			}
		}
	}
	
	console.log(`detail_level迭代完成`)

	return result
}

// 运行测试
async function runTest() {
	try {
		console.log('📋 测试1: 基本buildPromptStruct调用')
		
		const args = {
			char_id: 'test',
			UserCharname: 'User',
			Charname: 'Char',
			ReplyToCharname: 'User',
			chat_log: []
		}
		
		const result = await buildPromptStruct(args, 2)
		console.log('✅ buildPromptStruct调用成功')
		
		console.log('📋 验证结果结构...')
		console.log('结果类型:', typeof result)
		console.log('char_id:', result.char_id)
		console.log('UserCharname:', result.UserCharname)
		console.log('ReplyToCharname:', result.ReplyToCharname)
		console.log('Charname:', result.Charname)
		console.log('char_prompt文本数量:', result.char_prompt.text.length)
		console.log('user_prompt文本数量:', result.user_prompt.text.length)
		
		if (result.char_id === 'test' && result.UserCharname === 'User' && result.ReplyToCharname === 'User') {
			console.log('✅ 基本字段验证通过')
		} else {
			console.log('❌ 基本字段验证失败')
		}
		
		console.log('\n🎉 独立测试通过！')
		
	} catch (error) {
		console.error('❌ 测试失败:', error.message)
		console.error('错误堆栈:', error.stack)
	}
}

runTest()
