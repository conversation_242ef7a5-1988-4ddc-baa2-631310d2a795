#!/usr/bin/env deno run --allow-all
/**
 * Telegram 机器人配置验证脚本
 * 验证配置是否正确，但不启动实际的机器人
 */

console.log('🔍 GentianAphrodite Telegram 配置验证')
console.log('====================================')

async function verifyTelegramSetup() {
	let allTestsPassed = true

	// 1. 检查配置文件
	console.log('\n📋 检查配置文件...')
	try {
		let config
		if (await fileExists('./config.json')) {
			const configText = await Deno.readTextFile('./config.json')
			config = JSON.parse(configText)
			console.log('✅ config.json 文件存在且格式正确')
		} else if (await fileExists('./config.example.json')) {
			const configText = await Deno.readTextFile('./config.example.json')
			config = JSON.parse(configText)
			console.log('⚠️  使用 config.example.json（请复制为 config.json 并配置）')
		} else {
			console.log('❌ 未找到配置文件')
			allTestsPassed = false
			return allTestsPassed
		}

		// 检查 Telegram 配置
		if (config.telegram) {
			console.log('✅ Telegram 配置段存在')
			
			if (config.telegram.token && config.telegram.token !== 'YOUR_TELEGRAM_BOT_TOKEN') {
				console.log('✅ 机器人令牌已配置')
			} else {
				console.log('❌ 机器人令牌未配置或使用默认值')
				allTestsPassed = false
			}

			if (config.telegram.ownerUserId && config.telegram.ownerUserId !== 'YOUR_TELEGRAM_USER_ID') {
				console.log('✅ 主人用户ID已配置')
			} else {
				console.log('❌ 主人用户ID未配置或使用默认值')
				allTestsPassed = false
			}

			if (config.telegram.ownerUsername && config.telegram.ownerUsername !== 'YOUR_TELEGRAM_USERNAME') {
				console.log('✅ 主人用户名已配置')
			} else {
				console.log('⚠️  主人用户名未配置（可选）')
			}
		} else {
			console.log('❌ Telegram 配置段不存在')
			allTestsPassed = false
		}

	} catch (error) {
		console.log('❌ 配置文件解析失败:', error.message)
		allTestsPassed = false
	}

	// 2. 检查核心模块
	console.log('\n🔧 检查核心模块...')
	try {
		const GentianAphrodite = await import('./main.mjs')
		console.log('✅ 主模块加载成功')

		if (GentianAphrodite.default?.interfaces?.telegram) {
			console.log('✅ Telegram 接口存在')
			
			const configTemplate = await GentianAphrodite.default.interfaces.telegram.GetBotConfigTemplate()
			if (configTemplate) {
				console.log('✅ 配置模板获取成功')
			} else {
				console.log('❌ 配置模板获取失败')
				allTestsPassed = false
			}
		} else {
			console.log('❌ Telegram 接口不存在')
			allTestsPassed = false
		}
	} catch (error) {
		console.log('❌ 核心模块加载失败:', error.message)
		allTestsPassed = false
	}

	// 3. 检查 Telegram 模块
	console.log('\n📱 检查 Telegram 模块...')
	try {
		const telegramModule = await import('./interfaces/telegram/index.mjs')
		console.log('✅ Telegram 接口模块加载成功')

		if (telegramModule.TelegramBotMain && telegramModule.GetBotConfigTemplate) {
			console.log('✅ Telegram 主要函数存在')
		} else {
			console.log('❌ Telegram 主要函数缺失')
			allTestsPassed = false
		}
	} catch (error) {
		console.log('❌ Telegram 模块加载失败:', error.message)
		allTestsPassed = false
	}

	// 4. 检查 Telegraf 依赖
	console.log('\n📦 检查 Telegraf 依赖...')
	try {
		const { Telegraf } = await import('npm:telegraf')
		console.log('✅ Telegraf 库加载成功')

		// 创建测试实例（不连接）
		const testBot = new Telegraf('TEST_TOKEN')
		if (testBot) {
			console.log('✅ Telegraf 实例创建成功')
		}
	} catch (error) {
		console.log('❌ Telegraf 库加载失败:', error.message)
		console.log('   请确保网络连接正常，Deno 可以访问 npm 包')
		allTestsPassed = false
	}

	// 5. 检查工具函数
	console.log('\n🛠️  检查工具函数...')
	try {
		const tools = await import('./interfaces/telegram/tools.mjs')
		console.log('✅ Telegram 工具模块加载成功')

		// 测试基本功能
		const testText = '**测试** *文本* `代码`'
		const htmlResult = tools.aiMarkdownToTelegramHtml(testText)
		if (htmlResult.includes('<b>') && htmlResult.includes('<i>') && htmlResult.includes('<code>')) {
			console.log('✅ Markdown 转换功能正常')
		} else {
			console.log('❌ Markdown 转换功能异常')
			allTestsPassed = false
		}

		const splitResult = tools.splitTelegramReply('A'.repeat(5000), 4096)
		if (splitResult.length > 1) {
			console.log('✅ 消息分割功能正常')
		} else {
			console.log('❌ 消息分割功能异常')
			allTestsPassed = false
		}
	} catch (error) {
		console.log('❌ 工具函数检查失败:', error.message)
		allTestsPassed = false
	}

	return allTestsPassed
}

async function fileExists(path) {
	try {
		await Deno.stat(path)
		return true
	} catch {
		return false
	}
}

// 运行验证
const success = await verifyTelegramSetup()

console.log('\n' + '='.repeat(50))
if (success) {
	console.log('🎉 所有检查通过！Telegram 机器人配置正确。')
	console.log('')
	console.log('📝 下一步：')
	console.log('1. 确保配置文件中的机器人令牌是真实有效的')
	console.log('2. 运行: deno run --allow-all app.mjs --platform telegram')
	console.log('3. 在 Telegram 中测试机器人功能')
} else {
	console.log('❌ 检查发现问题，请修复后重试。')
	console.log('')
	console.log('📖 参考文档：')
	console.log('- TELEGRAM-SETUP.md - 详细设置指南')
	console.log('- README-STANDALONE.md - 应用使用指南')
}
console.log('')
