#!/usr/bin/env deno run --allow-all
/**
 * 测试定时器模块修复
 */

console.log('🧪 开始测试定时器模块修复...')

try {
	// 测试定时器模块导入
	console.log('📦 测试定时器模块导入...')
	const { getTimers, setTimer, removeTimer, initTimers } = await import('./lib/timers.mjs')
	console.log('✅ 定时器模块导入成功')

	// 测试 reply_gener/functions/timer.mjs 导入
	console.log('📦 测试 timer 功能模块导入...')
	const { timer, timerCallBack } = await import('./reply_gener/functions/timer.mjs')
	console.log('✅ timer 功能模块导入成功')

	// 测试定时器系统初始化
	console.log('🔧 测试定时器系统初始化...')
	await initTimers()
	console.log('✅ 定时器系统初始化成功')

	// 测试定时器基本功能
	console.log('⏰ 测试定时器基本功能...')
	
	// 获取空的定时器列表
	const emptyTimers = await getTimers('test_user', 'chars', 'test_char')
	console.log('📋 空定时器列表:', Object.keys(emptyTimers).length === 0 ? '✅ 正确' : '❌ 错误')

	// 设置一个测试定时器
	await setTimer('test_user', 'chars', 'test_char', 'test_timer_1', {
		trigger: 'Date.now() >= ' + (Date.now() + 1000), // 1秒后触发
		callbackdata: {
			type: 'timer',
			reason: '测试定时器',
			trigger: 'Date.now() >= ' + (Date.now() + 1000),
			chat_log_snip: '测试聊天记录',
			platform: 'test'
		},
		repeat: false
	})
	console.log('✅ 定时器设置成功')

	// 获取定时器列表
	const timersWithOne = await getTimers('test_user', 'chars', 'test_char')
	console.log('📋 定时器列表:', Object.keys(timersWithOne).length === 1 ? '✅ 正确' : '❌ 错误')

	// 移除定时器
	await removeTimer('test_user', 'chars', 'test_char', 'test_timer_1')
	console.log('✅ 定时器移除成功')

	// 验证定时器已移除
	const timersAfterRemove = await getTimers('test_user', 'chars', 'test_char')
	console.log('📋 移除后定时器列表:', Object.keys(timersAfterRemove).length === 0 ? '✅ 正确' : '❌ 错误')

	console.log('\n🎉 所有测试通过！定时器模块修复成功！')

} catch (error) {
	console.error('❌ 测试失败:', error.message)
	console.error('📍 错误堆栈:', error.stack)
	Deno.exit(1)
}
