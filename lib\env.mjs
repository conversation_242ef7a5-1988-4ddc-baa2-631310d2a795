/**
 * 环境检测工具
 * 本地实现，替代 fount 平台的 env 脚本
 */

/**
 * 检测是否在 Docker 容器中运行
 * @returns {boolean} 是否在 Docker 中
 */
export function in_docker() {
	try {
		// 检查 /.dockerenv 文件
		if (Deno.statSync('/.dockerenv').isFile) {
			return true
		}
	} catch {
		// 文件不存在，继续其他检查
	}

	try {
		// 检查 /proc/1/cgroup 文件
		const cgroupContent = Deno.readTextFileSync('/proc/1/cgroup')
		if (cgroupContent.includes('docker') || cgroupContent.includes('containerd')) {
			return true
		}
	} catch {
		// 文件不存在或无法读取
	}

	try {
		// 检查环境变量
		if (Deno.env.get('DOCKER_CONTAINER') === 'true') {
			return true
		}
	} catch {
		// 无法访问环境变量
	}

	return false
}

/**
 * 检测是否在 Termux 环境中运行
 * @returns {boolean} 是否在 Termux 中
 */
export function in_termux() {
	try {
		// 检查 Termux 特有的环境变量
		const prefix = Deno.env.get('PREFIX')
		if (prefix && prefix.includes('com.termux')) {
			return true
		}

		// 检查 TERMUX_VERSION 环境变量
		if (Deno.env.get('TERMUX_VERSION')) {
			return true
		}

		// 检查 Termux 特有的路径
		if (prefix && prefix === '/data/data/com.termux/files/usr') {
			return true
		}
	} catch {
		// 无法访问环境变量
	}

	try {
		// 检查 Termux 特有的文件
		if (Deno.statSync('/data/data/com.termux/files/usr/bin/termux-info').isFile) {
			return true
		}
	} catch {
		// 文件不存在
	}

	return false
}

/**
 * 检测操作系统类型
 * @returns {string} 操作系统类型 ('windows', 'darwin', 'linux', 'unknown')
 */
export function getOS() {
	return Deno.build.os
}

/**
 * 检测 CPU 架构
 * @returns {string} CPU 架构 ('x86_64', 'aarch64', 'unknown')
 */
export function getArch() {
	return Deno.build.arch
}

/**
 * 检测是否在 Windows 系统中
 * @returns {boolean} 是否为 Windows
 */
export function isWindows() {
	return Deno.build.os === 'windows'
}

/**
 * 检测是否在 macOS 系统中
 * @returns {boolean} 是否为 macOS
 */
export function isMacOS() {
	return Deno.build.os === 'darwin'
}

/**
 * 检测是否在 Linux 系统中
 * @returns {boolean} 是否为 Linux
 */
export function isLinux() {
	return Deno.build.os === 'linux'
}

/**
 * 检测是否有图形界面
 * @returns {boolean} 是否有图形界面
 */
export function hasGUI() {
	try {
		if (isWindows()) {
			// Windows 通常有图形界面
			return true
		}

		if (isMacOS()) {
			// macOS 通常有图形界面
			return true
		}

		if (isLinux()) {
			// 检查 DISPLAY 环境变量
			const display = Deno.env.get('DISPLAY')
			if (display) {
				return true
			}

			// 检查 WAYLAND_DISPLAY 环境变量
			const waylandDisplay = Deno.env.get('WAYLAND_DISPLAY')
			if (waylandDisplay) {
				return true
			}

			// 检查 XDG_SESSION_TYPE
			const sessionType = Deno.env.get('XDG_SESSION_TYPE')
			if (sessionType === 'x11' || sessionType === 'wayland') {
				return true
			}
		}

		return false
	} catch {
		return false
	}
}

/**
 * 检测是否在 CI/CD 环境中
 * @returns {boolean} 是否在 CI/CD 中
 */
export function inCI() {
	try {
		const ciEnvVars = [
			'CI',
			'CONTINUOUS_INTEGRATION',
			'GITHUB_ACTIONS',
			'GITLAB_CI',
			'TRAVIS',
			'CIRCLECI',
			'JENKINS_URL',
			'BUILDKITE',
			'DRONE'
		]

		for (const envVar of ciEnvVars) {
			if (Deno.env.get(envVar)) {
				return true
			}
		}

		return false
	} catch {
		return false
	}
}

/**
 * 获取环境信息摘要
 * @returns {Object} 环境信息对象
 */
export function getEnvironmentInfo() {
	return {
		os: getOS(),
		arch: getArch(),
		isWindows: isWindows(),
		isMacOS: isMacOS(),
		isLinux: isLinux(),
		inDocker: in_docker(),
		inTermux: in_termux(),
		hasGUI: hasGUI(),
		inCI: inCI(),
		denoVersion: Deno.version.deno,
		v8Version: Deno.version.v8,
		typescriptVersion: Deno.version.typescript
	}
}

/**
 * 打印环境信息
 */
export function printEnvironmentInfo() {
	const info = getEnvironmentInfo()
	console.log('环境信息:')
	console.log(`  操作系统: ${info.os} (${info.arch})`)
	console.log(`  Docker: ${info.inDocker ? '是' : '否'}`)
	console.log(`  Termux: ${info.inTermux ? '是' : '否'}`)
	console.log(`  图形界面: ${info.hasGUI ? '是' : '否'}`)
	console.log(`  CI/CD: ${info.inCI ? '是' : '否'}`)
	console.log(`  Deno 版本: ${info.denoVersion}`)
	console.log(`  V8 版本: ${info.v8Version}`)
	console.log(`  TypeScript 版本: ${info.typescriptVersion}`)
}
