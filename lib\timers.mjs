/**
 * 定时器管理模块
 * 提供定时器的创建、管理和执行功能
 */

import { loadJsonFileIfExists, saveJsonFile } from './json_loader.mjs'
import path from 'node:path'

// 定时器存储结构
const timers = new Map()
const timerIntervals = new Map()

// 定时器数据文件路径
const TIMERS_DATA_DIR = './data/timers'
const TIMER_CHECK_INTERVAL = 5000 // 5秒检查一次

/**
 * 定时器数据结构
 * @typedef {Object} TimerData
 * @property {string} trigger - 触发条件（JavaScript 表达式）
 * @property {Object} callbackdata - 回调数据
 * @property {boolean} repeat - 是否重复执行
 * @property {number} createdAt - 创建时间
 * @property {number} lastChecked - 上次检查时间
 */

/**
 * 获取定时器存储路径
 * @param {string} username - 用户名
 * @param {string} category - 分类
 * @param {string} id - ID
 * @returns {string} 文件路径
 */
function getTimerFilePath(username, category, id) {
	return path.join(TIMERS_DATA_DIR, `${username}_${category}_${id}.json`)
}

/**
 * 获取定时器键
 * @param {string} username - 用户名
 * @param {string} category - 分类
 * @param {string} id - ID
 * @returns {string} 定时器键
 */
function getTimerKey(username, category, id) {
	return `${username}:${category}:${id}`
}

/**
 * 加载定时器数据
 * @param {string} username - 用户名
 * @param {string} category - 分类
 * @param {string} id - ID
 */
async function loadTimersData(username, category, id) {
	const key = getTimerKey(username, category, id)
	if (!timers.has(key)) {
		const filePath = getTimerFilePath(username, category, id)
		const data = await loadJsonFileIfExists(filePath, {})
		timers.set(key, data)
	}
	return timers.get(key)
}

/**
 * 保存定时器数据
 * @param {string} username - 用户名
 * @param {string} category - 分类
 * @param {string} id - ID
 */
async function saveTimersData(username, category, id) {
	const key = getTimerKey(username, category, id)
	const data = timers.get(key) || {}
	const filePath = getTimerFilePath(username, category, id)
	
	// 确保目录存在
	const dir = path.dirname(filePath)
	try {
		await Deno.mkdir(dir, { recursive: true })
	} catch (error) {
		if (error.code !== 'EEXIST') {
			console.error('创建定时器目录失败:', error)
		}
	}
	
	await saveJsonFile(filePath, data)
}

/**
 * 获取指定用户的所有定时器
 * @param {string} username - 用户名
 * @param {string} category - 分类
 * @param {string} id - ID
 * @returns {Object} 定时器对象
 */
export async function getTimers(username, category, id) {
	await loadTimersData(username, category, id)
	const key = getTimerKey(username, category, id)
	return timers.get(key) || {}
}

/**
 * 设置定时器
 * @param {string} username - 用户名
 * @param {string} category - 分类
 * @param {string} id - ID
 * @param {string} uid - 定时器唯一ID
 * @param {TimerData} timerData - 定时器数据
 */
export async function setTimer(username, category, id, uid, timerData) {
	await loadTimersData(username, category, id)
	const key = getTimerKey(username, category, id)
	const userTimers = timers.get(key) || {}
	
	// 添加创建时间和初始检查时间
	const now = Date.now()
	userTimers[uid] = {
		...timerData,
		createdAt: now,
		lastChecked: now
	}
	
	timers.set(key, userTimers)
	await saveTimersData(username, category, id)
	
	// 启动定时器检查器（如果还没有启动）
	startTimerChecker()
	
	console.log(`定时器已设置: ${username}:${category}:${id}:${uid}`)
}

/**
 * 移除定时器
 * @param {string} username - 用户名
 * @param {string} category - 分类
 * @param {string} id - ID
 * @param {string} uid - 定时器唯一ID
 */
export async function removeTimer(username, category, id, uid) {
	await loadTimersData(username, category, id)
	const key = getTimerKey(username, category, id)
	const userTimers = timers.get(key) || {}
	
	if (userTimers[uid]) {
		delete userTimers[uid]
		timers.set(key, userTimers)
		await saveTimersData(username, category, id)
		console.log(`定时器已移除: ${username}:${category}:${id}:${uid}`)
	}
}

/**
 * 检查定时器触发条件
 * @param {string} trigger - 触发条件（JavaScript 表达式）
 * @returns {boolean} 是否触发
 */
function checkTrigger(trigger) {
	try {
		// 创建安全的执行环境
		const context = {
			Date,
			Math,
			console: { log: () => {} }, // 禁用 console.log
			setTimeout: () => {}, // 禁用 setTimeout
			setInterval: () => {}, // 禁用 setInterval
		}
		
		// 使用 Function 构造器创建函数并执行
		const func = new Function('context', `
			with (context) {
				return ${trigger};
			}
		`)
		
		return Boolean(func(context))
	} catch (error) {
		console.warn('定时器触发条件执行错误:', trigger, error.message)
		return false
	}
}

/**
 * 定时器检查器
 */
async function checkAllTimers() {
	const now = Date.now()
	
	for (const [key, userTimers] of timers.entries()) {
		const [username, category, id] = key.split(':')
		let hasChanges = false
		
		for (const [uid, timerData] of Object.entries(userTimers)) {
			// 避免频繁检查，至少间隔1秒
			if (now - timerData.lastChecked < 1000) {
				continue
			}
			
			timerData.lastChecked = now
			hasChanges = true
			
			// 检查触发条件
			if (checkTrigger(timerData.trigger)) {
				console.log(`定时器触发: ${key}:${uid}`)
				
				// 执行回调
				try {
					// 这里需要调用主应用的定时器回调
					if (globalThis.GentianAphroditeTimerCallback) {
						await globalThis.GentianAphroditeTimerCallback(username, uid, timerData.callbackdata)
					}
				} catch (error) {
					console.error('定时器回调执行错误:', error)
				}
				
				// 如果不是重复定时器，则删除
				if (!timerData.repeat) {
					delete userTimers[uid]
					hasChanges = true
					console.log(`一次性定时器已删除: ${key}:${uid}`)
				}
			}
		}
		
		// 如果有变化，保存数据
		if (hasChanges) {
			await saveTimersData(username, category, id)
		}
	}
}

/**
 * 启动定时器检查器
 */
function startTimerChecker() {
	if (!timerIntervals.has('main')) {
		const intervalId = setInterval(async () => {
			try {
				await checkAllTimers()
			} catch (error) {
				console.error('定时器检查器错误:', error)
			}
		}, TIMER_CHECK_INTERVAL)
		
		timerIntervals.set('main', intervalId)
		console.log('定时器检查器已启动')
	}
}

/**
 * 停止定时器检查器
 */
export function stopTimerChecker() {
	const intervalId = timerIntervals.get('main')
	if (intervalId) {
		clearInterval(intervalId)
		timerIntervals.delete('main')
		console.log('定时器检查器已停止')
	}
}

/**
 * 初始化定时器系统
 */
export async function initTimers() {
	// 确保数据目录存在
	try {
		await Deno.mkdir(TIMERS_DATA_DIR, { recursive: true })
	} catch (error) {
		if (error.code !== 'EEXIST') {
			console.error('创建定时器数据目录失败:', error)
		}
	}
	
	console.log('定时器系统已初始化')
}

/**
 * 清理定时器系统
 */
export function cleanupTimers() {
	stopTimerChecker()
	timers.clear()
	console.log('定时器系统已清理')
}

// 进程退出时清理
if (typeof Deno !== 'undefined') {
	try {
		Deno.addSignalListener('SIGINT', cleanupTimers)
		// Windows 不支持 SIGTERM，只在非 Windows 系统上添加
		if (Deno.build.os !== 'windows') {
			Deno.addSignalListener('SIGTERM', cleanupTimers)
		}
	} catch (error) {
		console.warn('信号监听器设置失败:', error.message)
	}
}
