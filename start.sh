#!/bin/bash

# GentianAphrodite 启动脚本
# 用于 Linux 和 macOS 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Deno 是否安装
check_deno() {
    if ! command -v deno &> /dev/null; then
        print_error "Deno 未安装或不在 PATH 中"
        print_info "请访问 https://deno.land/manual/getting_started/installation 安装 Deno"
        exit 1
    fi
    
    local deno_version=$(deno --version | head -n1 | cut -d' ' -f2)
    print_success "检测到 Deno 版本: $deno_version"
}

# 检查配置文件
check_config() {
    if [ ! -f "config.json" ]; then
        if [ -f "config.example.json" ]; then
            print_warning "未找到 config.json，正在复制示例配置文件..."
            cp config.example.json config.json
            print_info "已创建 config.json，请根据需要修改配置"
        else
            print_error "未找到配置文件"
            exit 1
        fi
    else
        print_success "找到配置文件: config.json"
    fi
}

# 创建必要的目录
create_directories() {
    local dirs=("data" "config" "logs" "i18n" "locales")
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_info "创建目录: $dir"
        fi
    done
}

# 显示帮助信息
show_help() {
    echo "GentianAphrodite 启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -p, --platform PLATFORM 指定平台 (shell, discord, telegram)"
    echo "  -u, --username USERNAME  指定用户名"
    echo "  -l, --locale LOCALE     指定语言 (zh-CN, en-US, ja-JP, ko-KR)"
    echo "  -c, --config CONFIG     指定配置文件路径"
    echo "  -d, --debug             启用调试模式"
    echo "  --check                 仅检查环境，不启动应用"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认设置启动"
    echo "  $0 -p discord -u Alice               # 启动 Discord 模式"
    echo "  $0 -p telegram -l en-US -d           # 启动 Telegram 模式（英文，调试）"
    echo "  $0 --check                           # 检查环境"
}

# 解析命令行参数
parse_args() {
    PLATFORM=""
    USERNAME=""
    LOCALE=""
    CONFIG=""
    DEBUG=""
    CHECK_ONLY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -p|--platform)
                PLATFORM="$2"
                shift 2
                ;;
            -u|--username)
                USERNAME="$2"
                shift 2
                ;;
            -l|--locale)
                LOCALE="$2"
                shift 2
                ;;
            -c|--config)
                CONFIG="$2"
                shift 2
                ;;
            -d|--debug)
                DEBUG="--debug"
                shift
                ;;
            --check)
                CHECK_ONLY=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 构建启动命令
build_command() {
    local cmd="deno run --allow-all app.mjs"
    
    if [ -n "$PLATFORM" ]; then
        cmd="$cmd --platform $PLATFORM"
    fi
    
    if [ -n "$USERNAME" ]; then
        cmd="$cmd --username \"$USERNAME\""
    fi
    
    if [ -n "$LOCALE" ]; then
        cmd="$cmd --locale $LOCALE"
    fi
    
    if [ -n "$CONFIG" ]; then
        cmd="$cmd --config \"$CONFIG\""
    fi
    
    if [ -n "$DEBUG" ]; then
        cmd="$cmd $DEBUG"
    fi
    
    echo "$cmd"
}

# 主函数
main() {
    print_info "GentianAphrodite 启动脚本"
    print_info "=========================="
    
    # 解析参数
    parse_args "$@"
    
    # 检查环境
    print_info "检查运行环境..."
    check_deno
    check_config
    create_directories
    
    if [ "$CHECK_ONLY" = true ]; then
        print_success "环境检查完成，一切正常！"
        exit 0
    fi
    
    # 构建并执行启动命令
    local cmd=$(build_command)
    print_info "启动命令: $cmd"
    print_info "正在启动 GentianAphrodite..."
    
    # 执行命令
    eval "$cmd"
}

# 错误处理
trap 'print_error "脚本执行失败，退出码: $?"' ERR

# 运行主函数
main "$@"
